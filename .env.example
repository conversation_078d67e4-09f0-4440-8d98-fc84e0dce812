# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-change-in-production

# OpenAI Configuration (Optional - for GPT models)
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
OPENAI_MODEL_NAME=gpt-4-vision-preview
OPENAI_API_VERSION=2023-12-01-preview

# Model Configuration
DEFAULT_MODEL=ultra-de-vlm-v1

# Logging
LOG_LEVEL=INFO

# Performance Settings
MAX_CONTENT_LENGTH=52428800  # 50MB in bytes
PDF_DPI=300
MAX_NEW_TOKENS=256000
TEMPERATURE=0.8
