# 🚀 **COMPLETE INSTALLATION COMMANDS**

## ✅ **ONE-COMMAND INSTALLATION**

### **Navigate to the directory and run:**

```bash
cd /home/<USER>/secondry_drive/mohith_data_management/VLM_WEB_DEMO/VLM_WEB
python setup_complete.py
```

**That's it! This single command will:**
- ✅ Fix all Flask/Jinja2 compatibility issues
- ✅ Install all required dependencies  
- ✅ Test the installation
- ✅ Start the web application automatically

---

## 🔧 **Alternative Commands (if needed):**

### **Option 1: Step-by-step manual installation**
```bash
# Navigate to directory
cd /home/<USER>/secondry_drive/mohith_data_management/VLM_WEB_DEMO/VLM_WEB

# Remove conflicting packages
pip uninstall Flask Jinja2 MarkupSafe Werkzeug -y

# Install compatible versions
pip install Flask==2.2.5 Jinja2==3.0.3 MarkupSafe==2.1.1 Werkzeug==2.2.3

# Install essential packages
pip install pandas numpy pillow requests python-dotenv PyPDF2 pdf2image openpyxl

# Start the application
python run_simple.py
```

### **Option 2: Quick fix runner**
```bash
cd /home/<USER>/secondry_drive/mohith_data_management/VLM_WEB_DEMO/VLM_WEB
python run_minimal.py
```

### **Option 3: Use requirements file**
```bash
cd /home/<USER>/secondry_drive/mohith_data_management/VLM_WEB_DEMO/VLM_WEB
pip install -r requirements_complete.txt
python run_web_app.py
```

---

## 📋 **Expected Output:**

When you run `python setup_complete.py`, you should see:

```
======================================================================
🚀 VLM WEB APPLICATION - COMPLETE SETUP
======================================================================

1️⃣ Checking Python Version
--------------------------------------------------
   Python version: 3.9.x
   ✅ Python version compatible

2️⃣ Removing Conflicting Packages
--------------------------------------------------
   Uninstalling Flask...
   ✅ Success

3️⃣ Installing Core Flask Ecosystem
--------------------------------------------------
   Installing Flask==2.2.5...
   ✅ Success
   Installing Jinja2==3.0.3...
   ✅ Success

6️⃣ Testing Critical Imports
--------------------------------------------------
   ✅ Flask import: SUCCESS
   ✅ Jinja2 escape import: SUCCESS
   ✅ Jinja2 Markup import: SUCCESS

======================================================================
🚀 INSTALLATION COMPLETED SUCCESSFULLY!
======================================================================
✅ All dependencies installed
✅ All imports working  
✅ Web application ready

🚀 Starting the web application...

9️⃣ Starting Web Application
--------------------------------------------------
   🌐 Starting server...
   📍 URL: http://localhost:5000
   🛑 Press Ctrl+C to stop

======================================================================
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
```

---

## 🎯 **After Installation:**

1. **Open your browser:** `http://localhost:5000`

2. **Test the complete workflow:**
   - Upload a file or paste text
   - Submit button will turn green: "Ready to process..."
   - Click "Submit & Process Document"
   - Results will display in table format

3. **Stop the server:** Press `Ctrl+C` in the terminal

---

## 🔧 **If You Have Issues:**

### **Permission Error:**
```bash
chmod +x setup_complete.py
python setup_complete.py
```

### **Python Not Found:**
```bash
python3 setup_complete.py
```

### **Import Errors:**
```bash
# Manual fix
pip uninstall Flask Jinja2 MarkupSafe Werkzeug -y
pip install Flask==2.2.5 Jinja2==3.0.3 MarkupSafe==2.1.1 Werkzeug==2.2.3
python run_simple.py
```

---

## ✅ **SUMMARY:**

**Just run this ONE command:**

```bash
cd /home/<USER>/secondry_drive/mohith_data_management/VLM_WEB_DEMO/VLM_WEB && python setup_complete.py
```

**Everything will be installed and the web application will start automatically!** 🎯
