# DocVu.AI - VLM Web Application

A powerful web-based document extraction service using Vision Language Models (VLMs) for intelligent document processing and data extraction.

## 🌟 Features

- **Multi-Model Support**: GPT-4V, Qwen2-VL, and text-only models
- **Document Types**: PDF, Images (JPG, PNG), and Text files
- **RAG Integration**: Automatic page selection for multi-page documents
- **Multiple Output Formats**: Key-value pairs, tables, and bullet points
- **Real-time Preview**: Document preview with page navigation
- **Download Options**: Excel and JSON export
- **Web Interface**: Modern, responsive Bootstrap UI
- **GPU Acceleration**: CUDA support for faster inference

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- CUDA-compatible GPU (optional, for better performance)
- 8GB+ RAM recommended

### Installation

1. **Clone or extract the VLM_WEB folder**
   ```bash
   cd VLM_WEB
   ```

2. **Run the startup script (Linux/Mac)**
   ```bash
   chmod +x run_web_app.sh
   ./run_web_app.sh
   ```

3. **Or run manually**
   ```bash
   # Create virtual environment
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   
   # Install dependencies
   pip install -r requirements.txt
   
   # Start the application
   python run_web_app.py
   ```

4. **Access the application**
   Open your browser and go to: `http://localhost:5000`

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the project root:

```bash
# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# OpenAI Configuration (for GPT models)
OPENAI_API_KEY=your-openai-api-key
OPENAI_ENDPOINT=your-azure-openai-endpoint
OPENAI_MODEL_NAME=gpt-4-vision-preview
OPENAI_API_VERSION=2023-12-01-preview

# Model Configuration
DEFAULT_MODEL=ultra-de-vlm-v1
```

### Model Setup

The application supports three types of models:

1. **ultra-de-vlm-v1**: Qwen2-VL based vision model (default)
2. **de-q2.5VL-3B**: Lightweight Qwen2.5-VL model
3. **ultra-de-v1**: Text-only model for document text

Models are automatically downloaded on first use.

## 📖 Usage

### 1. Upload Document
- Choose between file upload or text paste
- Supported formats: PDF, JPG, PNG, TXT
- Maximum file size: 50MB

### 2. Configure Processing
- Select the appropriate model
- Choose output format (key-value, table, bullet-points)
- Enter extraction prompt
- For PDFs: optionally specify page number

### 3. Process & Extract
- Click "Process Document"
- View real-time processing status
- Review structured and raw results

### 4. Download Results
- Export as Excel spreadsheet
- Download as JSON file

## 🎯 Example Prompts

- `Extract borrower information and co borrower information from section 3 only.`
- `Extract mortgage information section only.`
- `Extract all financial data including income, assets, and liabilities.`
- `Extract property details and address information.`
- `Extract loan terms, interest rate, and payment information.`

## 🏗️ Architecture

```
VLM_WEB/
├── app.py                 # Main Flask application
├── config.py             # Configuration settings
├── requirements.txt      # Python dependencies
├── run_web_app.py        # Application runner
├── run_web_app.sh        # Startup script
├── templates/            # HTML templates
│   ├── base.html
│   └── index.html
├── static/              # CSS, JS, and assets
│   ├── css/style.css
│   └── js/
├── src/                 # Core modules
│   ├── vlm_manager.py   # Model management
│   └── utils.py         # Utility functions
├── docvu_vlm_demo/      # Model implementations
│   └── models/
├── uploads/             # Uploaded files
├── temp/               # Temporary processing files
└── logs/               # Application logs
```

## 🔍 API Endpoints

- `GET /` - Main application interface
- `POST /upload` - File upload endpoint
- `POST /process` - Document processing
- `GET /download/<format>` - Result download
- `GET /models` - Available models list

## 🛠️ Development

### Adding New Models

1. Create model class in `docvu_vlm_demo/models/`
2. Implement `generate()` and `text_generate()` methods
3. Update `VLMManager` in `src/vlm_manager.py`
4. Add model configuration in `config.py`

### Customizing UI

- Modify templates in `templates/`
- Update styles in `static/css/style.css`
- Add JavaScript functionality in `static/js/`

## 🚨 Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   - Reduce batch size or use CPU mode
   - Close other GPU applications

2. **Model Download Fails**
   - Check internet connection
   - Verify Hugging Face access

3. **File Upload Errors**
   - Check file size (max 50MB)
   - Verify file format support

4. **RAG Not Working**
   - Install byaldi: `pip install byaldi`
   - Check PDF file integrity

### Performance Optimization

- Use GPU for faster inference
- Enable model caching
- Optimize image resolution
- Use appropriate model size

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For issues and questions:
- Check the troubleshooting section
- Review application logs in `logs/`
- Open an issue on the project repository

## 🔄 Updates

To update the application:
```bash
git pull origin main
pip install -r requirements.txt --upgrade
```

---

**DocVu.AI** - Intelligent Document Processing with Vision Language Models
