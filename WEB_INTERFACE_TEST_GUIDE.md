# 🧪 **VLM Web Interface - Complete Testing Guide**

## 🎯 **Step-by-Step Testing Instructions**

### **1. Start the Application**
```bash
cd /home/<USER>/secondry_drive/mohith_data_management/VLM_WEB/VLM_WEB
python run_web_app.py
```

### **2. Open Browser**
- Navigate to: `http://localhost:5000`
- You should see the DocVu.AI interface

### **3. Test File Upload & Submit Button**

#### **Option A: Upload PDF/Image File**
1. **Click "Upload File" tab** (should be selected by default)
2. **Click "Choose File" button**
3. **Select a PDF, JPG, PNG, or TXT file**
4. **Wait for upload** - you should see:
   - ✅ Success message: "File uploaded successfully!"
   - 📄 Document preview on the right panel
   - 🔘 **Submit button should now be ENABLED and say "Process Document"**

#### **Option B: Paste Text**
1. **Click "Paste Text" tab**
2. **Enter text in the textarea** (e.g., loan application text)
3. **Submit button should be ENABLED**

### **4. Configure Processing Options**

#### **Required Settings:**
- **Model**: Select from dropdown (Ultra DE VLM v1, Qwen2.5-VL 3B, Ultra DE v1)
- **Output Format**: Key-Value, Table, or Bullet Points
- **Prompt**: Enter extraction prompt (default: "Extract important information.")

#### **Optional Settings:**
- **Page Number**: For PDFs only (leave empty for auto-selection)

### **5. Submit & Process**

1. **Click the "Process Document" button**
2. **You should see:**
   - 🔄 Loading modal with "Processing document..."
   - ⏱️ Processing time indicator
   - 📊 **Results displayed in TABLE FORMAT below**

### **6. Verify Results Display**

#### **Expected Results Section:**
```
✅ Extraction Results                    ⏱️ 2.34s

[Download Excel] [Download JSON]

📊 Structured Results:
┌─────────────────┬─────────────────┬─────────────────┐
│ Field           │ Value           │ Details         │
├─────────────────┼─────────────────┼─────────────────┤
│ Borrower Name   │ John Smith      │ Primary         │
│ Loan Amount     │ $250,000        │ Requested       │
│ Property        │ 123 Main St     │ Address         │
└─────────────────┴─────────────────┴─────────────────┘

📝 Raw Output:
{
  "borrower_name": "John Smith",
  "loan_amount": "$250,000",
  "property_address": "123 Main St"
}
```

## 🔧 **Troubleshooting Common Issues**

### **Issue 1: Submit Button Not Appearing**
**Symptoms**: No submit button visible
**Solution**: 
- Check browser console for JavaScript errors (F12 → Console)
- Refresh the page
- Ensure JavaScript is enabled

### **Issue 2: Submit Button Disabled**
**Symptoms**: Button shows "Upload Document First" and is grayed out
**Solutions**:
- **For File Mode**: Upload a file first
- **For Text Mode**: Enter text in the textarea
- Check file size (must be < 50MB)
- Check file format (PDF, JPG, PNG, TXT only)

### **Issue 3: No Results Table**
**Symptoms**: Processing completes but no table appears
**Solutions**:
- Check if "Structured Results" section appears
- Look for error messages in red alerts
- Try different output format (Table, Key-Value, Bullet Points)
- Check browser console for errors

### **Issue 4: Upload Fails**
**Symptoms**: File upload shows error message
**Solutions**:
- Check file size (< 50MB)
- Check file format (PDF, JPG, PNG, TXT)
- Ensure server is running
- Check server logs for errors

## 🧪 **Test Cases to Verify**

### **Test Case 1: Text Processing**
1. Click "Paste Text" tab
2. Paste: "Borrower: John Smith, Loan: $250,000, Property: 123 Main St"
3. Select Model: "Ultra DE v1 (Text Only)"
4. Click "Process Document"
5. **Expected**: Table with extracted borrower information

### **Test Case 2: PDF Processing**
1. Upload a PDF file
2. Select Model: "Ultra DE VLM v1 (Vision + Text)"
3. Enter prompt: "Extract all financial information"
4. Click "Process Document"
5. **Expected**: Table with financial data from PDF

### **Test Case 3: Image Processing**
1. Upload a JPG/PNG image of a document
2. Select Model: "Qwen2.5-VL 3B (Vision + Text)"
3. Enter prompt: "Extract text from this image"
4. Click "Process Document"
5. **Expected**: Table with extracted text

## 🎯 **Success Criteria**

### **✅ Interface Working Correctly When:**
1. **Submit button appears and enables after file upload/text input**
2. **Processing modal shows during inference**
3. **Results appear in structured table format**
4. **Download buttons work (Excel/JSON)**
5. **No JavaScript errors in browser console**
6. **Server logs show successful processing**

### **📊 Expected Table Format:**
- **Headers**: Field names (e.g., "Borrower Name", "Loan Amount")
- **Rows**: Extracted values
- **Styling**: Bootstrap table with alternating row colors
- **Responsive**: Table scrolls horizontally on small screens

## 🚨 **If Issues Persist**

### **Check Browser Console:**
1. Press F12 to open Developer Tools
2. Go to Console tab
3. Look for red error messages
4. Report any JavaScript errors

### **Check Server Logs:**
- Look at terminal where `python run_web_app.py` is running
- Check for error messages or stack traces

### **Verify File Permissions:**
- Ensure `uploads/` directory exists and is writable
- Check that all static files (CSS/JS) are accessible

## 🎉 **Expected Final Result**

When working correctly, you should be able to:
1. **Upload documents or paste text**
2. **See enabled submit button**
3. **Process with AI models**
4. **View results in clean table format**
5. **Download results as Excel/JSON**

**The web interface should work EXACTLY like the original Streamlit GUI but with a better user experience!**
