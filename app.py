from flask import Flask, render_template, request, jsonify, send_file, session, Response

# Fix Jinja2 compatibility issues
try:
    from jinja2 import escape
except ImportError:
    from markupsafe import escape

try:
    from jinja2 import Markup
except ImportError:
    from markupsafe import Markup
from werkzeug.utils import secure_filename
import os
import time
import uuid
import json
import base64
from io import BytesIO
from datetime import datetime
import pandas as pd
from PIL import Image
import tempfile
import shutil

# Import the existing modules
import sys
import os
# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from src.vlm_manager import VLMManager
from src.utils import extract_pages_from_pdf
try:
    from docvu_vlm_demo.models import ColPaliRAG
except ImportError:
    print("Warning: ColPaliRAG not available. RAG functionality will be disabled.")
    ColPaliRAG = None

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # Change this in production

# Configuration - matches original GUI
UPLOAD_FOLDER = 'uploads'
TEMP_FOLDER = 'temp'
ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg', 'txt'}

# Temp directories - matches original GUI exactly
temp_pdf_dir = "./temp/input_files"
temp_images_dir = "./temp/images"

# Create necessary directories
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(temp_pdf_dir, exist_ok=True)
os.makedirs(temp_images_dir, exist_ok=True)

# Global variables for models - matches original GUI
vlm_manager = None
rag_model = None
current_model_type = None

# RAG configuration - matches original GUI
need_rag = True
col_rag = None  # Will be loaded when needed

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def load_vlm_manager(model_type):
    """Load VLM manager with specific model type - matches original GUI"""
    return VLMManager(model_type=model_type)

def load_rag_model():
    """Load RAG model - matches original GUI"""
    if ColPaliRAG is not None:
        try:
            return ColPaliRAG()
        except Exception as e:
            print(f"Warning: Could not initialize RAG model: {e}")
            return None
    return None

def initialize_models():
    """Initialize the models on first request"""
    global rag_model
    if rag_model is None:
        rag_model = load_rag_model()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/test-submit')
def test_submit():
    """Test page for submit button functionality"""
    return send_file('debug_submit_button.html')

@app.route('/debug')
def debug():
    """Debug interface for testing functionality"""
    return send_file('debug_interface.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    try:
        initialize_models()
        
        if 'file' not in request.files and 'text_content' not in request.form:
            return jsonify({'error': 'No file or text provided'}), 400
        
        # Handle text input
        if 'text_content' in request.form and request.form['text_content'].strip():
            text_content = request.form['text_content'].strip()
            session['text_content'] = text_content
            session['file_path'] = None
            session['file_type'] = 'text'
            return jsonify({
                'success': True, 
                'message': 'Text content received',
                'file_type': 'text'
            })
        
        # Handle file upload
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if file and allowed_file(file.filename):
            # File naming logic - matches original GUI exactly
            from datetime import datetime
            ist_now = datetime.now()
            timestamp = ist_now.strftime("%d%m%Y%H%M%S")

            # Count existing files to append number - matches original
            n = len([
                name for name in os.listdir(temp_pdf_dir)
                if os.path.isfile(os.path.join(temp_pdf_dir, name))
            ])
            updated_file_name = f"{n}_{timestamp}_{file.filename}"

            # Save file to temp directory - matches original
            file_path = os.path.join(temp_pdf_dir, updated_file_name)
            file.save(file_path)
            
            # Store in session
            session['file_path'] = file_path
            session['original_filename'] = file.filename
            session['updated_filename'] = updated_file_name
            session['text_content'] = None

            # Determine file type - matches original GUI logic
            is_pdf = "pdf" in file.content_type if file.content_type else file.filename.lower().endswith('.pdf')
            is_text = file.content_type == "text/plain" or file.filename.lower().endswith(".txt")

            if is_pdf:
                session['file_type'] = 'pdf'
            elif is_text:
                session['file_type'] = 'text'
            else:
                session['file_type'] = 'image'
            
            # Generate preview for PDF
            preview_data = None
            if session['file_type'] == 'pdf':
                try:
                    preview_images = extract_pages_from_pdf(file_path)
                    if isinstance(preview_images, list):
                        # Convert first few pages to base64 for preview
                        preview_data = []
                        for i, img in enumerate(preview_images[:5]):  # First 5 pages
                            buf = BytesIO()
                            img.save(buf, format="PNG")
                            b64 = base64.b64encode(buf.getvalue()).decode()
                            preview_data.append({
                                'page': i + 1,
                                'image': f"data:image/png;base64,{b64}"
                            })
                except Exception as e:
                    print(f"Error generating preview: {e}")
            
            return jsonify({
                'success': True,
                'message': 'File uploaded successfully',
                'filename': session['original_filename'],
                'file_type': session['file_type'],
                'preview': preview_data
            })
        
        return jsonify({'error': 'Invalid file type'}), 400
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/upload_text', methods=['POST'])
def upload_text():
    """Handle text content upload"""
    try:
        data = request.get_json()
        text_content = data.get('text_content', '').strip()

        if not text_content:
            return jsonify({'error': 'No text content provided'}), 400

        # Store text in session
        session['text_content'] = text_content
        session['file_path'] = None
        session['file_type'] = 'text'
        session['original_filename'] = 'pasted_text.txt'

        return jsonify({
            'success': True,
            'message': 'Text uploaded successfully',
            'text_length': len(text_content),
            'file_type': 'text'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/process', methods=['POST'])
def process_document():
    try:
        initialize_models()
        global current_model_type
        
        data = request.get_json()
        prompt = data.get('prompt', 'Extract important information.')
        input_format = data.get('input_format', 'key-value')
        model_type = data.get('model_type', 'ultra-de-vlm-v1')
        page_number = data.get('page_number')
        
        # Load model - matches original GUI workflow
        vlm_manager = load_vlm_manager(model_type)
        current_model_type = model_type
        
        start_time = time.time()
        
        # Process based on input type - matches original GUI logic
        if session.get('text_content'):
            # Pasted text processing - matches original: modified_prompt = pasted_text+prompt
            text_content = session['text_content']
            modified_prompt = text_content + prompt  # Same as original GUI
            results_dict = vlm_manager.docvqa_function(prompt=modified_prompt, image_path=None, input_format=input_format)
            
        elif session.get('file_path'):
            file_path = session['file_path']
            file_type = session['file_type']
            
            if file_type == 'pdf':
                # PDF processing with optional RAG
                from PyPDF2 import PdfReader
                reader = PdfReader(file_path)
                total_pages = len(reader.pages)
                
                if page_number:
                    selected_page = int(page_number)
                elif total_pages == 1:
                    selected_page = 1
                else:
                    # Use RAG to find relevant page - matches original GUI exactly
                    if need_rag and rag_model:
                        try:
                            search_results = rag_model.get_page_from_rag(file_path, prompt)
                            page_number = search_results[0]["page_num"]
                            if page_number < 1 or page_number > total_pages:
                                page_number = 1
                            selected_page = page_number
                        except Exception as e:
                            print(f"RAG failed: {e}")
                            selected_page = 1
                    else:
                        selected_page = 1
                
                # Extract page as image
                image_path = extract_pages_from_pdf(file_path, selected_page)
                results_dict = vlm_manager.docvqa_function(prompt, image_path, input_format=input_format)
                
            elif file_type == 'text':
                # Text file processing
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    text_content = f.read()
                combined_prompt = f"{prompt}\n\n{text_content}"
                results_dict = vlm_manager.docvqa_function(combined_prompt, image_path=None, input_format=input_format)
                
            else:
                # Image processing - matches original GUI exactly
                image_path = os.path.join(temp_images_dir, session['original_filename'])
                img = Image.open(file_path)
                img.save(image_path)
                results_dict = vlm_manager.docvqa_function(prompt, image_path, input_format=input_format)
        
        else:
            return jsonify({'error': 'No file or text content found'}), 400
        
        elapsed_time = time.time() - start_time
        
        # Format results - matches original GUI exactly
        formatted_output = results_dict.get('formatted_vlm_output', {})
        raw_output = results_dict.get('raw_vlm_output', '')

        # Clean raw output - matches original GUI
        raw_output_str = raw_output.replace("```", "").replace("json", "") if raw_output else ""
        
        # Convert to DataFrame for structured display
        result_df = None
        if formatted_output:
            result_df = VLMManager.format_results_into_table(formatted_output)
            
        # Prepare response - matches original GUI
        response_data = {
            'success': True,
            'elapsed_time': round(elapsed_time, 4),  # Matches original precision
            'raw_output': raw_output_str,
            'structured_data': result_df.to_dict('records') if result_df is not None and not result_df.empty else [],
            'columns': result_df.columns.tolist() if result_df is not None and not result_df.empty else []
        }
        
        # Store results in session for download - matches original GUI
        session['last_results'] = {
            'structured_data': result_df.to_dict('records') if result_df is not None and not result_df.empty else [],
            'raw_output': raw_output_str,
            'dataframe': result_df.to_json() if result_df is not None and not result_df.empty else None,
            'result_df': result_df,
            'elapsed_time': round(elapsed_time, 4)
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/download/<format_type>')
def download_results(format_type):
    try:
        if 'last_results' not in session:
            return jsonify({'error': 'No results to download'}), 400
        
        results = session['last_results']
        
        if format_type == 'excel':
            if results['dataframe']:
                df = pd.read_json(results['dataframe'])
                
                # Create Excel file in memory
                output = BytesIO()
                with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
                    df.to_excel(writer, index=False, sheet_name='Results')
                output.seek(0)
                
                return send_file(
                    output,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    as_attachment=True,
                    download_name='extraction_results.xlsx'
                )
            else:
                return jsonify({'error': 'No structured data to export'}), 400
                
        elif format_type == 'json':
            output = BytesIO()
            json_data = {
                'structured_data': results['structured_data'],
                'raw_output': results['raw_output']
            }
            output.write(json.dumps(json_data, indent=2).encode())
            output.seek(0)
            
            return send_file(
                output,
                mimetype='application/json',
                as_attachment=True,
                download_name='extraction_results.json'
            )
        
        return jsonify({'error': 'Invalid format type'}), 400
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/models')
def get_available_models():
    """Return available model types"""
    models = [
        {'value': 'ultra-de-vlm-v1', 'name': 'Ultra DE VLM v1 (Vision + Text)'},
        {'value': 'de-q2.5VL-3B', 'name': 'Qwen2.5-VL 3B (Vision + Text)'},
        {'value': 'ultra-de-v1', 'name': 'Ultra DE v1 (Text Only)'}
    ]
    return jsonify(models)

@app.route('/download/excel')
def download_excel():
    """Download results as Excel file"""
    try:
        if 'results' not in session:
            return jsonify({'error': 'No results to download'}), 400

        import pandas as pd
        from io import BytesIO

        results = session['results']
        df = pd.DataFrame([results])

        # Create Excel file in memory
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Extracted_Data', index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='extracted_data.xlsx'
        )

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/download/json')
def download_json():
    """Download results as JSON file"""
    try:
        if 'results' not in session:
            return jsonify({'error': 'No results to download'}), 400

        results = session['results']

        from io import StringIO
        import json

        json_str = json.dumps(results, indent=2, ensure_ascii=False)

        return Response(
            json_str,
            mimetype='application/json',
            headers={'Content-Disposition': 'attachment; filename=extracted_data.json'}
        )

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
