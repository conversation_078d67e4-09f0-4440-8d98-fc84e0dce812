import os
from pathlib import Path

class Config:
    """Base configuration class"""
    
    # Flask Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # File Upload Configuration
    MAX_CONTENT_LENGTH = 50 * 1024 * 1024  # 50MB max file size
    UPLOAD_FOLDER = 'uploads'
    TEMP_FOLDER = 'temp'
    ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg', 'txt'}
    
    # Model Configuration
    DEFAULT_MODEL = 'ultra-de-vlm-v1'
    AVAILABLE_MODELS = {
        'ultra-de-vlm-v1': {
            'name': 'Ultra DE VLM v1',
            'type': 'vision',
            'description': 'Advanced vision-language model for document extraction'
        },
        'de-q2.5VL-3B': {
            'name': 'Qwen2.5-VL 3B',
            'type': 'vision', 
            'description': 'Lightweight vision-language model'
        },
        'ultra-de-v1': {
            'name': 'Ultra DE v1',
            'type': 'text',
            'description': 'Text-only model for document extraction'
        }
    }
    
    # RAG Configuration
    RAG_ENABLED = True
    RAG_MODEL = "vidore/colqwen2-v1.0"
    RAG_INDEX_PATH = ".byaldi"
    
    # OpenAI Configuration (if using GPT models)
    OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
    OPENAI_ENDPOINT = os.environ.get('OPENAI_ENDPOINT')
    OPENAI_MODEL_NAME = os.environ.get('OPENAI_MODEL_NAME', 'gpt-4-vision-preview')
    OPENAI_API_VERSION = os.environ.get('OPENAI_API_VERSION', '2023-12-01-preview')
    
    # Processing Configuration
    MAX_NEW_TOKENS = 256000
    TEMPERATURE = 0.8
    PDF_DPI = 300
    MAX_PREVIEW_PAGES = 5
    
    # Logging Configuration
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = 'logs/app.log'
    
    # Cache Configuration
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 300
    
    @staticmethod
    def init_app(app):
        """Initialize application with configuration"""
        # Create necessary directories
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(Config.TEMP_FOLDER, exist_ok=True)
        os.makedirs(os.path.join(Config.TEMP_FOLDER, 'images'), exist_ok=True)
        os.makedirs(os.path.join(Config.TEMP_FOLDER, 'input_files'), exist_ok=True)
        os.makedirs('logs', exist_ok=True)

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'production-secret-key-must-be-changed'
    
    # Production-specific settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'production-default-secret-key-change-me'
    
    # Enhanced security
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'

class TestingConfig(Config):
    """Testing configuration"""
    DEBUG = True
    TESTING = True
    WTF_CSRF_ENABLED = False

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
