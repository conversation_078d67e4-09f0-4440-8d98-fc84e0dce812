<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VLM Interface Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .enabled { background-color: #d4edda; }
        .disabled { background-color: #f8d7da; }
        button { padding: 10px 20px; margin: 5px; }
        textarea { width: 100%; height: 100px; }
        input[type="file"] { margin: 10px 0; }
        .status { font-weight: bold; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🧪 VLM Web Interface Debug Tool</h1>
    
    <div class="section">
        <h3>📁 File Upload Test</h3>
        <input type="file" id="testFileInput" accept=".pdf,.jpg,.jpeg,.png,.txt">
        <div class="status" id="fileStatus">No file selected</div>
        <button id="testUploadBtn" disabled>Test Upload</button>
    </div>
    
    <div class="section">
        <h3>📝 Text Input Test</h3>
        <textarea id="testTextArea" placeholder="Enter test text here..."></textarea>
        <div class="status" id="textStatus">No text entered</div>
        <button id="testTextBtn" disabled>Test Text Processing</button>
    </div>
    
    <div class="section">
        <h3>🔘 Submit Button Logic Test</h3>
        <div>
            <label>
                <input type="radio" name="mode" value="file" checked> File Mode
            </label>
            <label>
                <input type="radio" name="mode" value="text"> Text Mode
            </label>
        </div>
        <button id="submitBtn" disabled>Submit & Process Document</button>
        <div class="status" id="submitStatus">Button should enable when file uploaded or text entered</div>
    </div>
    
    <div class="section">
        <h3>📊 API Test Results</h3>
        <div id="apiResults">No tests run yet</div>
    </div>

    <script>
        // Global variables
        let currentFile = null;
        let currentText = '';
        
        // DOM elements
        const fileInput = document.getElementById('testFileInput');
        const textArea = document.getElementById('testTextArea');
        const submitBtn = document.getElementById('submitBtn');
        const fileStatus = document.getElementById('fileStatus');
        const textStatus = document.getElementById('textStatus');
        const submitStatus = document.getElementById('submitStatus');
        const apiResults = document.getElementById('apiResults');
        const modeRadios = document.querySelectorAll('input[name="mode"]');
        
        // Update submit button logic
        function updateSubmitButton() {
            const mode = document.querySelector('input[name="mode"]:checked').value;
            let canSubmit = false;
            
            if (mode === 'file' && currentFile) {
                canSubmit = true;
                submitStatus.textContent = `File mode: ${currentFile.name} ready`;
                submitStatus.className = 'status enabled';
            } else if (mode === 'text' && currentText.trim()) {
                canSubmit = true;
                submitStatus.textContent = `Text mode: ${currentText.length} characters ready`;
                submitStatus.className = 'status enabled';
            } else {
                submitStatus.textContent = 'Waiting for file upload or text input...';
                submitStatus.className = 'status disabled';
            }
            
            submitBtn.disabled = !canSubmit;
            submitBtn.textContent = canSubmit ? 'Submit & Process Document' : 'Upload Document First';
        }
        
        // File input handler
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                currentFile = file;
                fileStatus.textContent = `Selected: ${file.name} (${(file.size/1024/1024).toFixed(2)} MB)`;
                fileStatus.className = 'status enabled';
            } else {
                currentFile = null;
                fileStatus.textContent = 'No file selected';
                fileStatus.className = 'status disabled';
            }
            updateSubmitButton();
        });
        
        // Text area handler
        textArea.addEventListener('input', function(e) {
            currentText = e.target.value;
            if (currentText.trim()) {
                textStatus.textContent = `Text entered: ${currentText.length} characters`;
                textStatus.className = 'status enabled';
            } else {
                textStatus.textContent = 'No text entered';
                textStatus.className = 'status disabled';
            }
            updateSubmitButton();
        });
        
        // Mode change handler
        modeRadios.forEach(radio => {
            radio.addEventListener('change', updateSubmitButton);
        });
        
        // Submit button handler
        submitBtn.addEventListener('click', async function() {
            const mode = document.querySelector('input[name="mode"]:checked').value;
            
            apiResults.innerHTML = '<div style="color: blue;">🔄 Testing API...</div>';
            
            try {
                if (mode === 'file' && currentFile) {
                    // Test file upload
                    const formData = new FormData();
                    formData.append('file', currentFile);
                    
                    const uploadResponse = await fetch('/upload', {
                        method: 'POST',
                        body: formData
                    });
                    
                    if (uploadResponse.ok) {
                        const uploadResult = await uploadResponse.json();
                        apiResults.innerHTML = `
                            <div style="color: green;">✅ File Upload Success!</div>
                            <pre>${JSON.stringify(uploadResult, null, 2)}</pre>
                        `;
                        
                        // Test processing
                        const processResponse = await fetch('/process', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                prompt: 'Extract important information',
                                input_format: 'table',
                                model_type: 'ultra-de-v1'
                            })
                        });
                        
                        if (processResponse.ok) {
                            const processResult = await processResponse.json();
                            apiResults.innerHTML += `
                                <div style="color: green;">✅ Processing Success!</div>
                                <div><strong>Processing Time:</strong> ${processResult.elapsed_time}s</div>
                                <div><strong>Structured Data:</strong> ${processResult.structured_data.length} records</div>
                                <pre>${JSON.stringify(processResult, null, 2)}</pre>
                            `;
                        } else {
                            apiResults.innerHTML += `<div style="color: red;">❌ Processing Failed: ${processResponse.status}</div>`;
                        }
                    } else {
                        apiResults.innerHTML = `<div style="color: red;">❌ Upload Failed: ${uploadResponse.status}</div>`;
                    }
                    
                } else if (mode === 'text' && currentText.trim()) {
                    // Test text upload
                    const textResponse = await fetch('/upload_text', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text_content: currentText })
                    });
                    
                    if (textResponse.ok) {
                        const textResult = await textResponse.json();
                        apiResults.innerHTML = `
                            <div style="color: green;">✅ Text Upload Success!</div>
                            <pre>${JSON.stringify(textResult, null, 2)}</pre>
                        `;
                        
                        // Test processing
                        const processResponse = await fetch('/process', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                prompt: 'Extract important information',
                                input_format: 'table',
                                model_type: 'ultra-de-v1'
                            })
                        });
                        
                        if (processResponse.ok) {
                            const processResult = await processResponse.json();
                            apiResults.innerHTML += `
                                <div style="color: green;">✅ Processing Success!</div>
                                <div><strong>Processing Time:</strong> ${processResult.elapsed_time}s</div>
                                <div><strong>Structured Data:</strong> ${processResult.structured_data.length} records</div>
                                <pre>${JSON.stringify(processResult, null, 2)}</pre>
                            `;
                        } else {
                            apiResults.innerHTML += `<div style="color: red;">❌ Processing Failed: ${processResponse.status}</div>`;
                        }
                    } else {
                        apiResults.innerHTML = `<div style="color: red;">❌ Text Upload Failed: ${textResponse.status}</div>`;
                    }
                }
                
            } catch (error) {
                apiResults.innerHTML = `<div style="color: red;">❌ Error: ${error.message}</div>`;
            }
        });
        
        // Initialize
        updateSubmitButton();
        
        // Test server connection on load
        fetch('/models')
            .then(response => response.json())
            .then(data => {
                console.log('✅ Server connected, available models:', data.models);
            })
            .catch(error => {
                console.error('❌ Server connection failed:', error);
                apiResults.innerHTML = '<div style="color: red;">❌ Server not accessible</div>';
            });
    </script>
</body>
</html>
