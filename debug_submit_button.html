<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Submit Button</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>🔧 Submit Button Debug Test</h2>
        
        <!-- Debug Info -->
        <div class="alert alert-info">
            <h5>Debug Information:</h5>
            <div id="debugInfo">Loading...</div>
        </div>
        
        <!-- Input Mode Selection -->
        <div class="mb-3">
            <label class="form-label fw-bold">Input Mode:</label>
            <div>
                <input type="radio" name="inputMode" value="file" id="fileMode" checked>
                <label for="fileMode">File Upload</label>
                
                <input type="radio" name="inputMode" value="text" id="textMode" class="ms-3">
                <label for="textMode">Text Input</label>
            </div>
        </div>
        
        <!-- File Upload Section -->
        <div id="fileUploadSection" class="mb-3">
            <label for="fileInput" class="form-label fw-bold">Upload Document:</label>
            <input type="file" class="form-control" id="fileInput" accept=".pdf,.png,.jpg,.jpeg,.txt">
        </div>
        
        <!-- Text Input Section -->
        <div id="textInputSection" class="mb-3" style="display: none;">
            <label for="textContent" class="form-label fw-bold">Text Content:</label>
            <textarea class="form-control" id="textContent" rows="5" placeholder="Paste your text content here..."></textarea>
        </div>
        
        <!-- Prompt Input -->
        <div class="mb-3">
            <label for="promptInput" class="form-label fw-bold">Extraction Prompt:</label>
            <input type="text" class="form-control" id="promptInput" value="Extract important information." placeholder="Enter your extraction prompt...">
        </div>
        
        <!-- Submit Button -->
        <div class="mb-4">
            <button type="button" class="btn btn-success btn-lg w-100" id="submitBtn" disabled>
                <i class="fas fa-play me-2"></i>Submit & Process Document
            </button>
            <div class="form-text text-center mt-2">
                <small id="submitStatus" class="text-muted">Please upload a file or enter text to enable processing</small>
            </div>
        </div>
        
        <!-- Preview Container -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-eye me-2"></i>Document Preview</h5>
                    </div>
                    <div class="card-body">
                        <div id="previewContainer">
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-file-alt fa-3x mb-3"></i>
                                <p>Upload a document to see preview</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Hidden elements for compatibility -->
        <div style="display: none;">
            <div id="pageNumberSection"></div>
            <select id="outputFormat"><option value="table">Table</option></select>
            <select id="modelSelect"><option value="ultra-de-vlm-v1">Ultra DE VLM v1</option></select>
            <input id="pageNumber" value="">
        </div>
        
        <!-- Debug Log -->
        <div class="mt-4">
            <h5>Debug Log:</h5>
            <div id="debugLog" class="bg-dark text-light p-3 rounded" style="height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Debug logging
        const debugLog = document.getElementById('debugLog');
        const debugInfo = document.getElementById('debugInfo');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }
        
        // Override console.log to capture all logs
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            log(args.join(' '));
        };
        
        // Override console.error
        const originalError = console.error;
        console.error = function(...args) {
            originalError.apply(console, args);
            log('ERROR: ' + args.join(' '));
        };
        
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 Debug page loaded');
            
            // Check if all elements exist
            const elements = {
                'fileInput': document.getElementById('fileInput'),
                'textContent': document.getElementById('textContent'),
                'submitBtn': document.getElementById('submitBtn'),
                'submitStatus': document.getElementById('submitStatus'),
                'previewContainer': document.getElementById('previewContainer'),
                'promptInput': document.getElementById('promptInput')
            };
            
            let allElementsFound = true;
            let elementStatus = '';
            
            for (const [name, element] of Object.entries(elements)) {
                if (element) {
                    elementStatus += `✅ ${name}: Found\n`;
                    log(`✅ Element found: ${name}`);
                } else {
                    elementStatus += `❌ ${name}: NOT FOUND\n`;
                    log(`❌ Element NOT found: ${name}`);
                    allElementsFound = false;
                }
            }
            
            debugInfo.innerHTML = `
                <strong>Element Check:</strong><br>
                <pre>${elementStatus}</pre>
                <strong>All Elements Found:</strong> ${allElementsFound ? '✅ YES' : '❌ NO'}
            `;
            
            if (!allElementsFound) {
                log('❌ Some elements are missing - submit button will not work');
                return;
            }
            
            // Test variables
            let currentFile = null;
            let currentFileType = null;
            let isProcessing = false;
            
            // Submit button update function
            function updateSubmitButton() {
                log('🔄 Updating submit button...');
                
                const inputMode = document.querySelector('input[name="inputMode"]:checked')?.value || 'file';
                const textValue = elements.textContent ? elements.textContent.value.trim() : '';
                
                let canProcess = false;
                let statusMessage = '';
                
                log(`📊 Current state: mode=${inputMode}, textLength=${textValue.length}, currentFile=${currentFile ? currentFile.name : 'null'}`);
                
                if (inputMode === 'text' && textValue) {
                    canProcess = true;
                    statusMessage = `Ready to process ${textValue.length} characters of text`;
                } else if (inputMode === 'file' && currentFile) {
                    canProcess = true;
                    statusMessage = `Ready to process file: ${currentFile.name}`;
                } else if (inputMode === 'text') {
                    statusMessage = 'Please enter text content to process';
                } else {
                    statusMessage = 'Please upload a file to process';
                }
                
                elements.submitBtn.disabled = !canProcess || isProcessing;
                elements.submitStatus.textContent = statusMessage;
                
                // Update button appearance
                elements.submitBtn.classList.remove('btn-secondary', 'btn-success', 'btn-warning');
                elements.submitStatus.classList.remove('text-muted', 'text-success', 'text-warning');
                
                if (canProcess && !isProcessing) {
                    elements.submitBtn.innerHTML = '<i class="fas fa-play me-2"></i>Submit & Process Document';
                    elements.submitBtn.classList.add('btn-success');
                    elements.submitStatus.classList.add('text-success');
                    log('✅ Submit button enabled (GREEN)');
                } else if (isProcessing) {
                    elements.submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                    elements.submitBtn.classList.add('btn-warning');
                    elements.submitStatus.classList.add('text-warning');
                    log('⏳ Submit button processing (YELLOW)');
                } else {
                    elements.submitBtn.innerHTML = '<i class="fas fa-upload me-2"></i>Submit & Process Document';
                    elements.submitBtn.classList.add('btn-secondary');
                    elements.submitStatus.classList.add('text-muted');
                    log('⏸️ Submit button disabled (GRAY)');
                }
                
                log(`🎯 Button state: disabled=${elements.submitBtn.disabled}, canProcess=${canProcess}, status="${statusMessage}"`);
            }
            
            // Document preview function
            function showDocumentPreview(file, fileType) {
                log(`🖼️ Showing preview for: ${file.name} (${fileType})`);
                
                elements.previewContainer.innerHTML = `
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>File Uploaded Successfully</h6>
                        <p class="mb-1"><strong>File:</strong> ${file.name}</p>
                        <p class="mb-1"><strong>Type:</strong> ${fileType}</p>
                        <p class="mb-0"><strong>Size:</strong> ${(file.size / 1024).toFixed(2)} KB</p>
                    </div>
                `;
                
                log('✅ Preview updated successfully');
            }
            
            // Input mode change handlers
            const inputModeRadios = document.querySelectorAll('input[name="inputMode"]');
            inputModeRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    log(`🔄 Input mode changed to: ${this.value}`);
                    
                    const fileUploadSection = document.getElementById('fileUploadSection');
                    const textInputSection = document.getElementById('textInputSection');
                    
                    if (this.value === 'file') {
                        fileUploadSection.style.display = 'block';
                        textInputSection.style.display = 'none';
                    } else {
                        fileUploadSection.style.display = 'none';
                        textInputSection.style.display = 'block';
                    }
                    
                    updateSubmitButton();
                });
            });
            
            // File input handler
            elements.fileInput.addEventListener('change', function(e) {
                log('📁 File input changed');
                const file = e.target.files[0];
                
                if (file) {
                    currentFile = file;
                    currentFileType = file.type.includes('pdf') ? 'pdf' : 
                                   file.type.includes('image') ? 'image' : 'text';
                    
                    log(`📄 File selected: ${file.name} (${currentFileType})`);
                    showDocumentPreview(file, currentFileType);
                } else {
                    currentFile = null;
                    currentFileType = null;
                    log('❌ No file selected');
                    elements.previewContainer.innerHTML = `
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-file-alt fa-3x mb-3"></i>
                            <p>Upload a document to see preview</p>
                        </div>
                    `;
                }
                
                updateSubmitButton();
            });
            
            // Text input handler
            elements.textContent.addEventListener('input', function() {
                log(`📝 Text content changed: ${this.value.length} characters`);
                updateSubmitButton();
            });
            
            // Submit button handler
            elements.submitBtn.addEventListener('click', function() {
                log('🚀 Submit button clicked!');
                alert('✅ Submit button is working!\n\nIn the real app, this would process the document.');
            });
            
            // Initialize
            log('🔧 Initializing submit button state...');
            updateSubmitButton();
            log('✅ Debug page initialization complete');
        });
    </script>
</body>
</html>
