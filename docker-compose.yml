version: '3.8'

services:
  vlm-web:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - SECRET_KEY=${SECRET_KEY:-default-secret-key}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_ENDPOINT=${OPENAI_ENDPOINT}
      - OPENAI_MODEL_NAME=${OPENAI_MODEL_NAME:-gpt-4-vision-preview}
    volumes:
      - ./uploads:/app/uploads
      - ./temp:/app/temp
      - ./logs:/app/logs
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 8G
        reservations:
          memory: 4G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add Redis for caching (uncomment if needed)
  # redis:
  #   image: redis:7-alpine
  #   restart: unless-stopped
  #   volumes:
  #     - redis_data:/data

# volumes:
#   redis_data:
