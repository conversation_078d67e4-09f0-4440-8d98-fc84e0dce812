try:
    from byaldi import RAGMultiModalModel
except ImportError:
    print("Warning: byaldi not installed. RAG functionality will be disabled.")
    RAGMultiModalModel = None

import time, shutil, os
import uuid
import json


class ColPaliRAG:
    def __init__(self):
        if RAGMultiModalModel is None:
            raise ImportError("byaldi package is required for RAG functionality. Please install it with: pip install byaldi")
        self.rag = RAGMultiModalModel.from_pretrained("vidore/colqwen2-v1.0")    
    
    def get_page_from_rag(self, pdf_path, text_query):
        st = time.time()
        print("UUID#######", str(uuid.uuid4()))
        if os.path.exists('./.byaldi'):
            print("Removing indexing directory")
            shutil.rmtree('./.byaldi')
        self.rag.index(
            input_path=pdf_path,
            index_name=str(uuid.uuid4()), # index will be saved at index_root/index_name/
            store_collection_with_index=False,
            overwrite=True
        )
        indexation_time = time.time()
        print("Time taken for indexation", round(indexation_time-st, 4))
        search_results = self.rag.search(
            text_query, k=10
        )
        print("search results!!", search_results)
        search_time = time.time()
        print("Time taken for search", round(search_time-indexation_time, 4))
        return search_results

def save_results_excel(config_path, search_results):
    pass

if __name__ == '__main__':

    find_page1 = "Extract Borrower information"
    find_page2 = "Extract VI Assets and liabilities"
    find_scheduleA = "Extract the address reference"
    find_risks = "Extract covered risks"
    find_employment = "Extract EMPLOYMENT INFORMATION?"
    find_note_latecharge = "Extract late charge for overdue payments"
    find_key_aus = "Extract maximum loan amount?"
    find_electronic_withdrawls = "Extract Electronic withdrawals"
    legal_description_finder = "Find the property description"
    loan_application_text = "1003 loan application document"
    tax_4506 =  "4506 Tax transcript document"
    note_text = "note document"
    deed_text = "warranty deed document"
    trust_text = "deed of trust or mortgage document"
    exhibit_text = "exhibit a - legal description document"
    family_rider_text = "1-4 family rider document"
    loan_application_text = "1003 loan application document"
    explanation_text = "letter or email of explanation document"
    appraisal_text = "appraisal report document"
    appraiser_license_text = "appraiser license document"
    appraisal_disclosure_text = "appraisal disclosure document"
    errors_omissions_text = "errors and omissions document"
    closing_disclosure_text = "closing disclosure document"
    closing_addendum_text = "addendum to closing disclosure document"
    first_payment_text = "first payment letter document"
    hazard_insurance_text = "hazard insurance policy document"
    insurance_documents_text = "insurance documents"
    escrow_disclosure_text = "initial escrow account disclosure statement document"
    fannie_mae_text = "fannie mae ucd findings report document"
    esign_consent_text = "esign consent document"
    tax_bills_text = "tax bills - receipts document"
    tax_info_text = "tax information sheet document"
    underwriting_summary_text = "1008 underwriting transmittal summary document"
    note_allonge_text = "note allonge document"
    flood_certificate_text = "flood hazard determination-flood certificate document"
    w9_text = "w9 - irs document"
    title_commitment_text = "commitment for title insurance document"
    privacy_policy_text = "privacy policy document"
    financial_privacy_text = "financial privacy notice document"
    borrower_auth_text = "borrower signature authorization document"
    chain_of_title_text = "chain of title document"
    purchase_sale_text = "purchase and sale agreement document"
    property_tax_text = "property tax related documents"
    invoices_text = "invoices document"

    pdf_path = "/path/to/test/document.pdf"
    text_query = "extract certificate of title"
    rag = ColPaliRAG()
    results = rag.get_page_from_rag(pdf_path, text_query)
    print(results)
