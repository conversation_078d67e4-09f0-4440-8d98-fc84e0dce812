import base64, time, os, csv
import logging

try:
    from openai import AzureOpenAI
    from openai import OpenAIError, RateLimitError, APIConnectionError, Timeout, AuthenticationError, PermissionDeniedError, BadRequestError
except ImportError:
    print("Warning: openai package not installed. GPT models will be disabled.")
    AzureOpenAI = None

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CDoCVuGPTClient:
    """Handles communication with Azure OpenAI GPT model for image inference."""

    def __init__(self):
        if AzureOpenAI is None:
            raise ImportError("openai package is required for GPT models. Please install it with: pip install openai")
        
        # Initialize API endpoint and credentials from environment variables
        self.endpoint = os.environ.get('OPENAI_ENDPOINT')
        self.deployment = os.environ.get('OPENAI_MODEL_NAME', 'gpt-4-vision-preview')
        self.subscription_key = os.environ.get('OPENAI_API_KEY')
        self.api_version = os.environ.get('OPENAI_API_VERSION', '2023-12-01-preview')
        
        if not all([self.endpoint, self.subscription_key]):
            raise ValueError("Please set OPENAI_ENDPOINT and OPENAI_API_KEY environment variables")
        
        # Initialize Azure OpenAI Service client with key-based authentication    
        self.client = AzureOpenAI(  
            azure_endpoint=self.endpoint,  
            api_key=self.subscription_key,  
            api_version=self.api_version,
        )
        self.system_prompt = "Assume you are a expert in extracting fields from the document"
        self.debug = False
    
    def text_generate(self, user_prompt):
        """Generate text completion from a given prompt (no image input)."""
        try:
            # Prepare chat prompt
            chat_prompt = [
                {
                    "role": "system",
                    "content": [
                        {
                            "type": "text",
                            "text": self.system_prompt
                        }
                    ]
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": user_prompt
                        }
                    ]
                }
            ]
            
            # Make API call
            response = self.client.chat.completions.create(
                model=self.deployment,
                messages=chat_prompt,
                max_tokens=4000,
                temperature=0.7
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Error in text generation: {e}")
            return None
    
    def generate(self, image_path, user_prompt):
        """Generate response from image and text prompt."""
        try:
            # Encode image to base64
            with open(image_path, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')
            
            # Prepare chat prompt with image
            chat_prompt = [
                {
                    "role": "system",
                    "content": [
                        {
                            "type": "text",
                            "text": self.system_prompt
                        }
                    ]
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": user_prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ]
            
            # Make API call
            response = self.client.chat.completions.create(
                model=self.deployment,
                messages=chat_prompt,
                max_tokens=4000,
                temperature=0.7
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Error in vision generation: {e}")
            return None

if __name__ == "__main__":
    # Test the client
    try:
        client = CDoCVuGPTClient()
        result = client.text_generate("Hello, how are you?")
        print(result)
    except Exception as e:
        print(f"Error: {e}")
