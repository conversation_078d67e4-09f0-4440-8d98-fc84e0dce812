import json, time, os
import logging

try:
    from transformers import Qwen2ForConditionalGeneration, AutoTokenizer
except ImportError:
    print("Warning: transformers not installed. Qwen text models will be disabled.")
    Qwen2ForConditionalGeneration = None
    AutoTokenizer = None

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CDoCVuQwenText:
    def __init__(self):
        if Qwen2ForConditionalGeneration is None:
            raise ImportError("transformers package is required for Qwen text models")
        
        # Load the text-only model and tokenizer
        model = Qwen2ForConditionalGeneration.from_pretrained(
            "Qwen/Qwen2.5-7B-Instruct",
            torch_dtype="auto",
            device_map="auto"
        )

        tokenizer = AutoTokenizer.from_pretrained(
            "Qwen/Qwen2.5-7B-Instruct"
        )
        self.model = model
        self.tokenizer = tokenizer
    
    def text_generate(self, prompt):
        """Generate text response from prompt"""
        try:
            st = time.time()

            # Prepare messages
            messages = [
                {"role": "system", "content": "You are a helpful assistant for document extraction."},
                {"role": "user", "content": prompt}
            ]
            
            # Apply chat template
            text = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )
            
            # Tokenize
            model_inputs = self.tokenizer([text], return_tensors="pt").to(self.model.device)

            # Generate
            generated_ids = self.model.generate(
                **model_inputs,
                max_new_tokens=4096,
                temperature=0.7,
                do_sample=True
            )
            
            # Decode
            generated_ids = [
                output_ids[len(input_ids):] for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
            ]
            
            response = self.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]

            et = time.time()
            logger.info(f"Time taken for text inference using Qwen: {round(et-st, 2)}")
            logger.info(f"RAW QWEN TEXT OUTPUT: {response}")
            
            return response

        except Exception as e:
            logger.error(f"An error occurred during text generation: {e}")
            return None
    
    def generate(self, image_path, prompt):
        """For compatibility - text model doesn't support images"""
        logger.warning("Text-only model called with image input. Ignoring image and processing text only.")
        return self.text_generate(prompt)

if __name__ == '__main__':
    try:
        client = CDoCVuQwenText()
        result = client.text_generate("Extract key information from this loan application document.")
        print(result)
    except Exception as e:
        print(f"Error: {e}")
