import json, time, os
import logging

try:
    from transformers import Qwen2VLForConditionalGeneration, AutoProcessor
    from qwen_vl_utils import process_vision_info
except ImportError:
    print("Warning: transformers or qwen_vl_utils not installed. Qwen models will be disabled.")
    Qwen2VLForConditionalGeneration = None
    AutoProcessor = None
    process_vision_info = None

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CDoCVuQwenClient:
    def __init__(self):
        if Qwen2VLForConditionalGeneration is None:
            raise ImportError("transformers and qwen_vl_utils packages are required for Qwen models")
        
        # Load the model and processor
        model = Qwen2VLForConditionalGeneration.from_pretrained(
            "Qwen/Qwen2-VL-7B-Instruct",
            torch_dtype="auto",
            device_map="auto"
        )

        processor = AutoProcessor.from_pretrained(
            "Qwen/Qwen2-VL-7B-Instruct"
        )
        self.model = model
        self.processor = processor
    
    def text_generate(self, prompt):
        output_dir = "./json_results"
        os.makedirs(output_dir, exist_ok=True)
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    }
                ]
            }
        ]
        try:
            st = time.time()

            # Preparation for inference
            text = self.processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
            image_inputs, video_inputs = process_vision_info(messages)
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt",
            )
            inputs = inputs.to("cuda")

            # Inference: Generate the output
            generated_ids = self.model.generate(**inputs, max_new_tokens=256000, temperature=0.8)
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=True
            )

            et = time.time()
            logger.info(f"Time taken for inference using Qwen: {round(et-st, 2)}")
            logger.info(f"RAW QWEN OUTPUT: {output_text}")
            
            return output_text[0] if output_text else None

        except Exception as e:
            logger.error(f"An error occurred during text generation: {e}")
            return None
    
    def generate(self, image_path, prompt):
        output_dir = "./json_results"
        os.makedirs(output_dir, exist_ok=True)
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image",
                        "image": image_path,
                        "resized_height": 1753,
                        "resized_width": 1240,
                    },
                    {
                        "type": "text",
                        "text": prompt
                    }
                ]
            }
        ]
        try:
            st = time.time()

            # Preparation for inference
            text = self.processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
            image_inputs, video_inputs = process_vision_info(messages)
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt",
            )
            inputs = inputs.to("cuda")

            # Inference: Generate the output
            generated_ids = self.model.generate(**inputs, max_new_tokens=256000, temperature=0.8)
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=True
            )

            et = time.time()
            logger.info(f"Time taken for inference using Qwen: {round(et-st, 2)}")
            logger.info(f"RAW QWEN OUTPUT: {output_text}")
            
            return output_text[0] if output_text else None

        except Exception as e:
            logger.error(f"An error occurred during vision generation: {e}")
            return None

if __name__ == '__main__':
    try:
        client = CDoCVuQwenClient()
        result = client.text_generate("Hello, how are you?")
        print(result)
    except Exception as e:
        print(f"Error: {e}")
