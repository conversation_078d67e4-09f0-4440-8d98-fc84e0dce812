import logging
import os
from datetime import datetime

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(levelname)s] [%(asctime)s] %(name)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log'),
        logging.StreamHandler()
    ]
)

# Create logger instance
logger = logging.getLogger('DocVuVLM')

def prepare_logger(name=None):
    """Prepare a logger instance"""
    if name:
        return logging.getLogger(name)
    return logger

# For backward compatibility
def info(message):
    logger.info(message)

def error(message):
    logger.error(message)

def warning(message):
    logger.warning(message)

def debug(message):
    logger.debug(message)
