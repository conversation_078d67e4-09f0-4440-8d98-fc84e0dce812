#!/usr/bin/env python3
"""
Quick fix for Flask/Jinja2 compatibility issues
Run this script to fix the import error
"""

import subprocess
import sys
import os

def run_command(cmd):
    """Run a command and return success status"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        print(f"Command: {cmd}")
        print(f"Return code: {result.returncode}")
        if result.stdout:
            print(f"Output: {result.stdout}")
        if result.stderr and result.returncode != 0:
            print(f"Error: {result.stderr}")
        return result.returncode == 0
    except Exception as e:
        print(f"Error running command: {e}")
        return False

def fix_flask_jinja_compatibility():
    """Fix Flask/Jinja2 compatibility issues"""
    
    print("🔧 FIXING FLASK/JINJA2 COMPATIBILITY ISSUES")
    print("=" * 60)
    
    # Method 1: Try upgrading Flask and related packages
    print("\n1️⃣ Upgrading Flask and related packages...")
    
    packages_to_upgrade = [
        "Flask==2.3.3",
        "Jinja2==3.1.2", 
        "MarkupSafe==2.1.3",
        "Werkzeug==2.3.7"
    ]
    
    for package in packages_to_upgrade:
        print(f"\nInstalling {package}...")
        success = run_command(f"pip install {package}")
        if not success:
            print(f"❌ Failed to install {package}")
        else:
            print(f"✅ Successfully installed {package}")
    
    # Method 2: Install from requirements file
    print("\n2️⃣ Installing from requirements file...")
    if os.path.exists("requirements_fixed.txt"):
        success = run_command("pip install -r requirements_fixed.txt")
        if success:
            print("✅ Requirements installed successfully")
        else:
            print("❌ Failed to install requirements")
    
    # Method 3: Alternative fix - downgrade Jinja2
    print("\n3️⃣ Alternative: Downgrading Jinja2 if needed...")
    success = run_command("pip install 'Jinja2<3.1'")
    if success:
        print("✅ Jinja2 downgraded successfully")
    
    print("\n" + "=" * 60)
    print("🧪 TESTING THE FIX...")
    
    # Test the imports
    try:
        from flask import Flask
        print("✅ Flask import: SUCCESS")

        # Test escape import
        try:
            from jinja2 import escape
            print("✅ jinja2.escape import: SUCCESS")
        except ImportError:
            from markupsafe import escape
            print("✅ markupsafe.escape import: SUCCESS (fallback)")

        # Test Markup import
        try:
            from jinja2 import Markup
            print("✅ jinja2.Markup import: SUCCESS")
        except ImportError:
            from markupsafe import Markup
            print("✅ markupsafe.Markup import: SUCCESS (fallback)")

        print("\n🎉 ALL COMPATIBILITY ISSUES FIXED!")
        print("\n🚀 You can now run the web application:")
        print("   python run_web_app.py")
        print("   OR")
        print("   python run_simple.py")
        return True

    except Exception as e:
        print(f"❌ Import test failed: {e}")
        print("\n🔧 MANUAL FIX REQUIRED:")
        print("   Try running: pip install Flask==2.2.5 Jinja2==3.0.3 MarkupSafe==2.1.1")
        return False

if __name__ == "__main__":
    success = fix_flask_jinja_compatibility()
    if not success:
        print("\n📋 MANUAL STEPS:")
        print("1. pip uninstall Flask Jinja2 MarkupSafe")
        print("2. pip install Flask==2.2.5 Jinja2==3.0.3 MarkupSafe==2.1.1")
        print("3. python run_web_app.py")
    
    sys.exit(0 if success else 1)
