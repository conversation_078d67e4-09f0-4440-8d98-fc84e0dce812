#!/usr/bin/env python3
"""
Install Compatible Dependencies for VLM Web Application
Ensures Flask/Jinja2 compatibility and installs required packages
"""

import subprocess
import sys
import os

def run_pip_command(command):
    """Run a pip command and return success status"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=True)
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr

def install_compatible_packages():
    """Install compatible packages in the correct order"""
    
    print("🔧 INSTALLING COMPATIBLE VLM WEB APPLICATION DEPENDENCIES")
    print("=" * 70)
    
    # Step 1: Uninstall potentially conflicting packages
    print("\n1️⃣ Removing potentially conflicting packages...")
    conflicting_packages = ["Flask", "Jinja2", "MarkupSafe", "Werkzeug"]
    
    for package in conflicting_packages:
        print(f"   Uninstalling {package}...")
        success, output = run_pip_command(f"pip uninstall {package} -y")
        if success:
            print(f"   ✅ {package} uninstalled")
        else:
            print(f"   ⚠️  {package} not found or already uninstalled")
    
    # Step 2: Install core Flask ecosystem with compatible versions
    print("\n2️⃣ Installing compatible Flask ecosystem...")
    core_packages = [
        "Flask==2.2.5",
        "Werkzeug==2.2.3", 
        "Jinja2==3.0.3",
        "MarkupSafe==2.1.1",
        "itsdangerous==2.1.2",
        "click==8.1.7"
    ]
    
    for package in core_packages:
        print(f"   Installing {package}...")
        success, output = run_pip_command(f"pip install {package}")
        if success:
            print(f"   ✅ {package} installed successfully")
        else:
            print(f"   ❌ Failed to install {package}")
            print(f"      Error: {output}")
            return False
    
    # Step 3: Install essential dependencies
    print("\n3️⃣ Installing essential dependencies...")
    essential_packages = [
        "pandas==2.0.3",
        "numpy==1.24.3",
        "Pillow==10.0.1",
        "requests==2.31.0",
        "python-dotenv==1.0.0"
    ]
    
    for package in essential_packages:
        print(f"   Installing {package}...")
        success, output = run_pip_command(f"pip install {package}")
        if success:
            print(f"   ✅ {package} installed successfully")
        else:
            print(f"   ⚠️  Warning: Failed to install {package}")
    
    # Step 4: Install document processing dependencies
    print("\n4️⃣ Installing document processing dependencies...")
    doc_packages = [
        "PyPDF2==3.0.1",
        "pdf2image==1.16.3",
        "openpyxl==3.1.2"
    ]
    
    for package in doc_packages:
        print(f"   Installing {package}...")
        success, output = run_pip_command(f"pip install {package}")
        if success:
            print(f"   ✅ {package} installed successfully")
        else:
            print(f"   ⚠️  Warning: Failed to install {package}")
    
    # Step 5: Test imports
    print("\n5️⃣ Testing imports...")
    try:
        from flask import Flask
        print("   ✅ Flask import: SUCCESS")
        
        from jinja2 import escape
        print("   ✅ Jinja2 escape import: SUCCESS")
        
        from jinja2 import Markup
        print("   ✅ Jinja2 Markup import: SUCCESS")
        
        import pandas as pd
        print("   ✅ Pandas import: SUCCESS")
        
        from PIL import Image
        print("   ✅ PIL import: SUCCESS")
        
    except ImportError as e:
        print(f"   ❌ Import test failed: {e}")
        return False
    
    print("\n" + "=" * 70)
    print("🎉 INSTALLATION COMPLETED SUCCESSFULLY!")
    print("\n📋 Next steps:")
    print("   1. Run the web application:")
    print("      python run_minimal.py")
    print("      OR")
    print("      python run_simple.py")
    print("\n   2. Open browser:")
    print("      http://localhost:5000")
    print("\n   3. Test the complete workflow:")
    print("      - Upload file or paste text")
    print("      - Submit button will turn green")
    print("      - Click 'Submit & Process Document'")
    print("      - Results will display in table format")
    print("\n✅ All compatibility issues resolved!")
    
    return True

def main():
    """Main function"""
    print("Starting compatible package installation...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8+ required")
        print(f"   Current version: {sys.version}")
        sys.exit(1)
    
    # Run installation
    success = install_compatible_packages()
    
    if not success:
        print("\n❌ Installation failed!")
        print("\n🔧 Manual steps:")
        print("1. pip uninstall Flask Jinja2 MarkupSafe Werkzeug -y")
        print("2. pip install Flask==2.2.5 Jinja2==3.0.3 MarkupSafe==2.1.1 Werkzeug==2.2.3")
        print("3. pip install pandas numpy pillow requests python-dotenv")
        print("4. python run_minimal.py")
        sys.exit(1)
    
    sys.exit(0)

if __name__ == "__main__":
    main()
