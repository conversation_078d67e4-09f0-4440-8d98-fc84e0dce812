#!/usr/bin/env python3
"""
Quick Setup Script for VLM Web Application
This script installs dependencies and verifies the installation
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and return success status"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"   ✅ Success")
        return True
    except subprocess.CalledProcessError as e:
        print(f"   ❌ Failed: {e}")
        if e.stdout:
            print(f"   Output: {e.stdout}")
        if e.stderr:
            print(f"   Error: {e.stderr}")
        return False

def main():
    print("🚀 VLM Web Application - Quick Setup")
    print("=" * 50)
    
    # Check Python version
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.8+")
        return False
    
    # Install core dependencies first - COMPATIBLE VERSIONS
    core_deps = [
        "Flask==2.2.5",
        "Werkzeug==2.2.3",
        "Jinja2==3.0.3",
        "MarkupSafe==2.1.1",
        "pandas",
        "numpy",
        "pillow",
        "pdf2image",
        "openpyxl",
        "xlsxwriter"
    ]
    
    print("\n📦 Installing core dependencies...")
    for dep in core_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            print(f"⚠️  Failed to install {dep}, continuing...")
    
    # Install ML dependencies
    ml_deps = [
        "torch",
        "torchvision", 
        "transformers"
    ]
    
    print("\n🤖 Installing ML dependencies...")
    for dep in ml_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            print(f"⚠️  Failed to install {dep}, continuing...")
    
    # Install optional dependencies
    optional_deps = [
        "openai",
        "sentence-transformers"
    ]
    
    print("\n📚 Installing optional dependencies...")
    for dep in optional_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            print(f"⚠️  {dep} not installed (optional)")
    
    # Create necessary directories
    print("\n📁 Creating directories...")
    dirs = ["uploads", "temp/images", "temp/input_files", "logs", "static/uploads"]
    for dir_path in dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"   ✅ Created {dir_path}")
    
    # Test installation
    print("\n🧪 Testing installation...")
    if run_command("python test_installation.py", "Running installation test"):
        print("\n🎉 Setup completed successfully!")
        print("🌐 You can now start the application with:")
        print("   python run_web_app.py")
        print("   or")
        print("   ./run_web_app.sh")
        print("\n📖 Access the application at: http://localhost:5000")
        return True
    else:
        print("\n⚠️  Setup completed with some issues.")
        print("🔧 Try running: pip install -r requirements.txt")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
