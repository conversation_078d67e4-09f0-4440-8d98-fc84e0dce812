# Complete Requirements for VLM Web Application
# All compatible versions tested and working together

# ===== CORE WEB FRAMEWORK - COMPATIBLE VERSIONS =====
Flask==2.2.5
Werkzeug==2.2.3
Jinja2==3.0.3
MarkupSafe==2.1.1
itsdangerous==2.1.2
click==8.1.7

# ===== ESSENTIAL DEPENDENCIES =====
# Data Processing
pandas==2.0.3
numpy==1.24.3
openpyxl==3.1.2

# Image Processing
Pillow==10.0.1
opencv-python==4.8.1.78

# PDF Processing
PyPDF2==3.0.1
pdf2image==1.16.3

# Utilities
requests==2.31.0
python-dotenv==1.0.0

# ===== OPTIONAL DEPENDENCIES (for full functionality) =====
# Document Processing
python-docx==0.8.11
xlsxwriter==3.1.1

# AI/ML Libraries (if using VLM models)
torch==2.0.1
torchvision==0.15.2
transformers==4.33.2
accelerate==0.21.0

# Vision Language Models (optional)
# qwen-vl-utils==0.0.1
# colpali-engine==0.2.0

# OpenAI Integration (optional)
# openai==0.28.1

# RAG and Embeddings (optional)
# sentence-transformers==2.2.2
# faiss-cpu==1.7.4
# chromadb==0.4.10

# Additional Image Processing (optional)
# scikit-image==0.21.0

# Logging (optional)
# loguru==0.7.2

# Development Tools (optional)
# pytest==7.4.2
# black==23.9.1
# flake8==6.1.0

# Production Servers (optional)
gunicorn==21.2.0
# waitress==2.1.2

# Performance (optional)
psutil==5.9.5

# ===== INSTALLATION NOTES =====
# 1. Install core dependencies first:
#    pip install Flask==2.2.5 Werkzeug==2.2.3 Jinja2==3.0.3 MarkupSafe==2.1.1
#
# 2. Install essential dependencies:
#    pip install pandas numpy pillow pdf2image openpyxl requests python-dotenv
#
# 3. Install optional dependencies as needed:
#    pip install torch transformers (for AI models)
#    pip install gunicorn (for production)
#
# 4. For complete installation:
#    pip install -r requirements_complete.txt
