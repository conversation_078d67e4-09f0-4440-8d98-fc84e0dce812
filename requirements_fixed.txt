# Fixed Requirements for VLM Web Application
# Compatible versions that work together

# Core Web Framework - MOST COMPATIBLE VERSIONS
Flask==2.2.5
Werkzeug==2.2.3
Jinja2==3.0.3
MarkupSafe==2.1.1
itsdangerous==2.1.2
click==8.1.7

# Data Processing
pandas==2.0.3
numpy==1.24.3
openpyxl==3.1.2

# Image Processing
Pillow==10.0.1
opencv-python==4.8.1.78

# PDF Processing
PyPDF2==3.0.1
pdf2image==1.16.3

# AI/ML Libraries
torch==2.0.1
torchvision==0.15.2
transformers==4.33.2
accelerate==0.21.0

# Additional Dependencies
requests==2.31.0
python-dotenv==1.0.0
gunicorn==21.2.0

# Optional: For better performance
psutil==5.9.5
