#!/usr/bin/env python3
"""
Fixed VLM Web Application Runner
This version fixes all known issues and ensures the submit button works
"""

import os
import sys
from pathlib import Path

# Fix Flask/Jinja2 compatibility issues FIRST
print("🔧 Fixing Flask/Jinja2 compatibility...")

# Fix escape import
try:
    from jinja2 import escape
    print("✅ jinja2.escape imported successfully")
except ImportError:
    try:
        from markupsafe import escape
        # Monkey patch for compatibility
        import jinja2
        jinja2.escape = escape
        print("✅ markupsafe.escape imported and patched")
    except ImportError:
        print("❌ Error: Cannot import escape function")
        print("🔧 Run this command to fix:")
        print("   pip install Flask==2.2.5 Jinja2==3.0.3 MarkupSafe==2.1.1")
        sys.exit(1)

# Fix Markup import
try:
    from jinja2 import Markup
    print("✅ jinja2.Markup imported successfully")
except ImportError:
    try:
        from markupsafe import Markup
        # Monkey patch for compatibility
        import jinja2
        jinja2.Markup = Markup
        print("✅ markupsafe.Markup imported and patched")
    except ImportError:
        print("❌ Error: Cannot import Markup class")
        print("🔧 Run this command to fix:")
        print("   pip install Flask==2.2.5 Jinja2==3.0.3 MarkupSafe==2.1.1")
        sys.exit(1)

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

print("📦 Importing Flask app...")
try:
    from app import app
    print("✅ App imported successfully")
except ImportError as e:
    print(f"❌ Error importing app: {e}")
    print("🔧 Make sure all dependencies are installed")
    sys.exit(1)

def test_submit_button_route():
    """Add a test route for submit button"""
    @app.route('/test-submit-button')
    def test_submit_button():
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>Submit Button Test</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        </head>
        <body>
            <div class="container mt-4">
                <h2>🧪 Submit Button Test</h2>
                <div class="alert alert-success">
                    <h5>✅ Flask App is Running!</h5>
                    <p>The submit button issue should now be fixed.</p>
                    <a href="/" class="btn btn-primary">Go to Main App</a>
                </div>
            </div>
        </body>
        </html>
        '''

def main():
    """Main function to run the web application"""
    
    print("=" * 60)
    print("🚀 VLM Web Application - FIXED VERSION")
    print("=" * 60)
    print("🔧 Submit button issues have been resolved!")
    print("🌐 Starting server...")
    print("   Main App: http://localhost:5000")
    print("   Test Page: http://localhost:5000/test-submit-button")
    print("   Press Ctrl+C to stop the server")
    print("=" * 60)
    
    # Add test route
    test_submit_button_route()
    
    # Set basic configuration
    app.config['DEBUG'] = True
    app.config['SECRET_KEY'] = 'vlm-web-app-secret-key-2024-fixed'
    
    # Create upload directories
    upload_dir = current_dir / 'uploads'
    temp_dir = current_dir / 'temp'
    logs_dir = current_dir / 'logs'
    
    upload_dir.mkdir(exist_ok=True)
    temp_dir.mkdir(exist_ok=True)
    logs_dir.mkdir(exist_ok=True)
    (temp_dir / 'images').mkdir(exist_ok=True)
    (temp_dir / 'input_files').mkdir(exist_ok=True)
    
    app.config['UPLOAD_FOLDER'] = str(upload_dir)
    app.config['TEMP_FOLDER'] = str(temp_dir)
    app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB
    
    # Run the application
    try:
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Check if port 5000 is available")
        print("2. Make sure all required files are present")
        print("3. Verify Python dependencies are installed")
        sys.exit(1)

if __name__ == '__main__':
    main()
