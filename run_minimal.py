#!/usr/bin/env python3
"""
Minimal VLM Web Application Runner
Uses the most compatible Flask/Jinja2 versions
"""

import os
import sys
import subprocess
from pathlib import Path

def install_compatible_packages():
    """Install the most compatible Flask/Jinja2 versions"""
    print("🔧 Installing compatible Flask/Jinja2 versions...")
    
    # Most compatible versions that work together
    packages = [
        "Flask==2.2.5",
        "Jinja2==3.0.3",
        "MarkupSafe==2.1.1",
        "Werkzeug==2.2.3",
        "itsdangerous==2.1.2",
        "click==8.1.7"
    ]
    
    for package in packages:
        print(f"Installing {package}...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package], 
                         check=True, capture_output=True)
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    return True

def test_imports():
    """Test if all imports work"""
    print("🧪 Testing imports...")
    
    try:
        from flask import Flask
        print("✅ Flask imported successfully")
        
        from jinja2 import escape, Markup
        print("✅ Jinja2 escape and Markup imported successfully")
        
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def main():
    """Main function"""
    print("=" * 60)
    print("🚀 VLM Web Application - Minimal Runner")
    print("=" * 60)
    
    # Try to import first
    if not test_imports():
        print("\n🔧 Installing compatible packages...")
        if not install_compatible_packages():
            print("❌ Failed to install packages")
            sys.exit(1)
        
        # Test again
        if not test_imports():
            print("❌ Still having import issues")
            print("🔧 Manual fix required:")
            print("   pip uninstall Flask Jinja2 MarkupSafe Werkzeug -y")
            print("   pip install Flask==2.2.5 Jinja2==3.0.3 MarkupSafe==2.1.1 Werkzeug==2.2.3")
            sys.exit(1)
    
    # Add paths
    current_dir = Path(__file__).parent
    sys.path.insert(0, str(current_dir))
    parent_dir = current_dir.parent
    sys.path.insert(0, str(parent_dir))
    
    # Import app
    print("📦 Importing Flask app...")
    try:
        from app import app
        print("✅ App imported successfully")
    except ImportError as e:
        print(f"❌ Error importing app: {e}")
        sys.exit(1)
    
    # Configure app
    app.config['DEBUG'] = True
    app.config['SECRET_KEY'] = 'vlm-web-app-secret-key-2024'
    
    # Create directories
    upload_dir = current_dir / 'uploads'
    temp_dir = current_dir / 'temp'
    upload_dir.mkdir(exist_ok=True)
    temp_dir.mkdir(exist_ok=True)
    (temp_dir / 'images').mkdir(exist_ok=True)
    (temp_dir / 'input_files').mkdir(exist_ok=True)
    
    app.config['UPLOAD_FOLDER'] = str(upload_dir)
    app.config['TEMP_FOLDER'] = str(temp_dir)
    app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB
    
    print("🌐 Starting server...")
    print("   Access the application at: http://localhost:5000")
    print("   Press Ctrl+C to stop the server")
    print("=" * 60)
    
    # Run the application
    try:
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
