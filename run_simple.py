#!/usr/bin/env python3
"""
Simple VLM Web Application Runner
Alternative runner that bypasses config issues
"""

import os
import sys
from pathlib import Path

# Fix Flask/Jinja2 compatibility issues FIRST
print("🔧 Fixing Flask/Jinja2 compatibility...")

# Fix escape import
try:
    from jinja2 import escape
    print("✅ jinja2.escape imported successfully")
except ImportError:
    try:
        from markupsafe import escape
        # Monkey patch for compatibility
        import jinja2
        jinja2.escape = escape
        print("✅ markupsafe.escape imported and patched")
    except ImportError:
        print("❌ Error: Cannot import escape function")
        print("🔧 Run this command to fix:")
        print("   pip install Flask==2.2.5 Jinja2==3.0.3 MarkupSafe==2.1.1")
        sys.exit(1)

# Fix Markup import
try:
    from jinja2 import Markup
    print("✅ jinja2.Markup imported successfully")
except ImportError:
    try:
        from markupsafe import Markup
        # Monkey patch for compatibility
        import jinja2
        jinja2.Markup = Markup
        print("✅ markupsafe.Markup imported and patched")
    except ImportError:
        print("❌ Error: Cannot import Markup class")
        print("🔧 Run this command to fix:")
        print("   pip install Flask==2.2.5 Jinja2==3.0.3 MarkupSafe==2.1.1")
        sys.exit(1)

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Add parent directory for original VLM modules
parent_dir = current_dir.parent
sys.path.insert(0, str(parent_dir))

print("📦 Importing Flask app...")
try:
    from app import app
    print("✅ App imported successfully")
except ImportError as e:
    print(f"❌ Error importing app: {e}")
    print("🔧 Make sure all dependencies are installed")
    sys.exit(1)

def main():
    """Main function to run the web application"""
    
    print("=" * 60)
    print("🚀 VLM Web Application - Simple Runner")
    print("=" * 60)
    print("🌐 Starting server...")
    print("   Access the application at: http://localhost:5000")
    print("   Press Ctrl+C to stop the server")
    print("=" * 60)
    
    # Set basic configuration
    app.config['DEBUG'] = True
    app.config['SECRET_KEY'] = 'vlm-web-app-secret-key-2024'
    
    # Create upload directories
    upload_dir = current_dir / 'uploads'
    temp_dir = current_dir / 'temp'
    upload_dir.mkdir(exist_ok=True)
    temp_dir.mkdir(exist_ok=True)
    
    app.config['UPLOAD_FOLDER'] = str(upload_dir)
    app.config['TEMP_FOLDER'] = str(temp_dir)
    app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB
    
    # Run the application
    try:
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Check if port 5000 is available")
        print("2. Run 'python fix_dependencies.py' to fix package issues")
        print("3. Make sure all required files are present")
        sys.exit(1)

if __name__ == '__main__':
    main()
