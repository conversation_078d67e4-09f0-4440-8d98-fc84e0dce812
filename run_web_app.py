#!/usr/bin/env python3
"""
DocVu.AI VLM Web Application Runner

This script starts the Flask web application for document extraction using Vision Language Models.
"""

import os
import sys
from pathlib import Path

# Fix Flask/Jinja2 compatibility issue
try:
    from jinja2 import escape
except ImportError:
    try:
        from markupsafe import escape
        # Monkey patch for compatibility
        import jinja2
        jinja2.escape = escape
    except ImportError:
        print("❌ Error: Cannot import escape function")
        print("🔧 Fix: Run 'python fix_dependencies.py' to resolve this issue")
        sys.exit(1)

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from app import app
    from config import config
except ImportError as e:
    print(f"❌ Error importing app: {e}")
    print("🔧 Fix: Run 'python fix_dependencies.py' to resolve dependency issues")
    sys.exit(1)

def main():
    """Main function to run the web application"""
    
    # Get environment
    env = os.environ.get('FLASK_ENV', 'development')
    
    # Load configuration
    app_config = config.get(env, config['default'])
    app.config.from_object(app_config)
    
    # Initialize app
    app_config.init_app(app)
    
    # Print startup information
    print("=" * 60)
    print("🚀 DocVu.AI VLM Web Application")
    print("=" * 60)
    print(f"Environment: {env}")
    print(f"Debug Mode: {app.config['DEBUG']}")
    print(f"Upload Folder: {app.config['UPLOAD_FOLDER']}")
    print(f"Max File Size: {app.config['MAX_CONTENT_LENGTH'] / (1024*1024):.0f}MB")
    print("=" * 60)
    print("📋 Available Models:")
    for model_id, model_info in app.config['AVAILABLE_MODELS'].items():
        print(f"  • {model_info['name']} ({model_info['type']})")
    print("=" * 60)
    print("🌐 Starting server...")
    print("   Access the application at: http://localhost:5000")
    print("   Press Ctrl+C to stop the server")
    print("=" * 60)
    
    # Run the application
    try:
        if env == 'production':
            # Production server
            from waitress import serve
            serve(app, host='0.0.0.0', port=5000)
        else:
            # Development server
            app.run(
                host='0.0.0.0',
                port=5000,
                debug=app.config['DEBUG'],
                threaded=True
            )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
