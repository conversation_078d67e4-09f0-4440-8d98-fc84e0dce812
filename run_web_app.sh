#!/bin/bash

# DocVu.AI VLM Web Application Startup Script

echo "🚀 Starting DocVu.AI VLM Web Application..."
echo "================================================"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed or not in PATH"
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install/upgrade requirements
echo "📥 Installing/updating dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p uploads
mkdir -p temp/images
mkdir -p temp/input_files
mkdir -p logs
mkdir -p static/uploads

# Set environment variables if not set
export FLASK_ENV=${FLASK_ENV:-development}
export FLASK_APP=app.py

# Check for GPU availability
if command -v nvidia-smi &> /dev/null; then
    echo "🎮 GPU detected:"
    nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits
else
    echo "⚠️  No GPU detected. Models will run on CPU (slower performance)"
fi

# Start the application
echo "🌐 Starting web server..."
echo "   Access the application at: http://localhost:5000"
echo "   Press Ctrl+C to stop the server"
echo "================================================"

python3 run_web_app.py
