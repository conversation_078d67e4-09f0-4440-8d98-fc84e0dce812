#!/usr/bin/env python3
"""
Complete VLM Web Application Setup
One-file installation that handles everything:
- Fixes Flask/Jinja2 compatibility
- Installs all required dependencies
- Tests the installation
- Starts the web application
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 70)
    print(f"🚀 {title}")
    print("=" * 70)

def print_step(step_num, title):
    """Print a formatted step"""
    print(f"\n{step_num} {title}")
    print("-" * 50)

def run_command(command, description=""):
    """Run a command and return success status"""
    if description:
        print(f"   {description}...")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=True)
        print(f"   ✅ Success")
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        print(f"   ❌ Failed: {e.stderr}")
        return False, e.stderr

def check_python_version():
    """Check if Python version is compatible"""
    print_step("1️⃣", "Checking Python Version")
    
    version = sys.version_info
    print(f"   Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 8):
        print("   ❌ Error: Python 3.8+ required")
        return False
    
    print("   ✅ Python version compatible")
    return True

def uninstall_conflicting_packages():
    """Remove potentially conflicting Flask packages"""
    print_step("2️⃣", "Removing Conflicting Packages")
    
    packages = ["Flask", "Jinja2", "MarkupSafe", "Werkzeug", "itsdangerous"]
    
    for package in packages:
        success, _ = run_command(f"pip uninstall {package} -y", f"Uninstalling {package}")
        if not success:
            print(f"   ⚠️  {package} not found (OK)")

def install_core_packages():
    """Install core Flask ecosystem with compatible versions"""
    print_step("3️⃣", "Installing Core Flask Ecosystem")
    
    # Most compatible versions that work together
    packages = [
        "Flask==2.2.5",
        "Werkzeug==2.2.3",
        "Jinja2==3.0.3", 
        "MarkupSafe==2.1.1",
        "itsdangerous==2.1.2",
        "click==8.1.7"
    ]
    
    for package in packages:
        success, _ = run_command(f"pip install {package}", f"Installing {package}")
        if not success:
            print(f"   ❌ Critical: Failed to install {package}")
            return False
    
    return True

def install_essential_packages():
    """Install essential dependencies for the web application"""
    print_step("4️⃣", "Installing Essential Dependencies")
    
    packages = [
        "pandas==2.0.3",
        "numpy==1.24.3",
        "Pillow==10.0.1",
        "requests==2.31.0",
        "python-dotenv==1.0.0",
        "PyPDF2==3.0.1",
        "pdf2image==1.16.3",
        "openpyxl==3.1.2"
    ]
    
    failed_packages = []
    for package in packages:
        success, _ = run_command(f"pip install {package}", f"Installing {package}")
        if not success:
            failed_packages.append(package)
            print(f"   ⚠️  Warning: {package} failed (may not be critical)")
    
    if failed_packages:
        print(f"   ⚠️  Some packages failed: {', '.join(failed_packages)}")
        print("   ℹ️  The web application may still work with basic functionality")
    
    return True

def install_optional_packages():
    """Install optional packages for enhanced functionality"""
    print_step("5️⃣", "Installing Optional Packages")
    
    packages = [
        "gunicorn==21.2.0",
        "psutil==5.9.5",
        "python-docx==0.8.11",
        "xlsxwriter==3.1.1"
    ]
    
    for package in packages:
        success, _ = run_command(f"pip install {package}", f"Installing {package}")
        if not success:
            print(f"   ⚠️  Optional: {package} failed (not critical)")

def test_imports():
    """Test if all critical imports work"""
    print_step("6️⃣", "Testing Critical Imports")
    
    tests = [
        ("Flask", "from flask import Flask"),
        ("Jinja2 escape", "from jinja2 import escape"),
        ("Jinja2 Markup", "from jinja2 import Markup"),
        ("Pandas", "import pandas as pd"),
        ("PIL", "from PIL import Image"),
        ("Requests", "import requests")
    ]
    
    failed_imports = []
    for name, import_statement in tests:
        try:
            exec(import_statement)
            print(f"   ✅ {name} import: SUCCESS")
        except ImportError as e:
            print(f"   ❌ {name} import: FAILED - {e}")
            failed_imports.append(name)
    
    if failed_imports:
        print(f"\n   ❌ Critical imports failed: {', '.join(failed_imports)}")
        return False
    
    print("   ✅ All critical imports successful!")
    return True

def create_directories():
    """Create necessary directories"""
    print_step("7️⃣", "Creating Required Directories")
    
    directories = [
        "uploads",
        "temp",
        "temp/images", 
        "temp/input_files",
        "logs"
    ]
    
    for directory in directories:
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            print(f"   ✅ Created: {directory}")
        except Exception as e:
            print(f"   ❌ Failed to create {directory}: {e}")

def test_web_application():
    """Test if the web application can start"""
    print_step("8️⃣", "Testing Web Application")
    
    # Test import of the main app
    try:
        print("   Testing app import...")
        sys.path.insert(0, os.getcwd())
        from app import app
        print("   ✅ App import: SUCCESS")
        
        # Test basic configuration
        app.config['TESTING'] = True
        app.config['SECRET_KEY'] = 'test-key'
        print("   ✅ App configuration: SUCCESS")
        
        return True
        
    except Exception as e:
        print(f"   ❌ App test failed: {e}")
        return False

def start_application():
    """Start the web application"""
    print_step("9️⃣", "Starting Web Application")
    
    print("   🌐 Starting server...")
    print("   📍 URL: http://localhost:5000")
    print("   🛑 Press Ctrl+C to stop")
    print("\n" + "=" * 70)
    
    try:
        # Import and configure app
        from app import app
        
        app.config['DEBUG'] = True
        app.config['SECRET_KEY'] = 'vlm-web-app-secret-key-2024'
        app.config['UPLOAD_FOLDER'] = 'uploads'
        app.config['TEMP_FOLDER'] = 'temp'
        app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB
        
        # Start the server
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        return False
    
    return True

def main():
    """Main installation and setup function"""
    print_header("VLM WEB APPLICATION - COMPLETE SETUP")
    print("This script will:")
    print("• Fix Flask/Jinja2 compatibility issues")
    print("• Install all required dependencies")
    print("• Test the installation")
    print("• Start the web application")
    
    # Step-by-step installation
    if not check_python_version():
        sys.exit(1)
    
    uninstall_conflicting_packages()
    
    if not install_core_packages():
        print("\n❌ Critical error: Core packages installation failed")
        sys.exit(1)
    
    install_essential_packages()
    install_optional_packages()
    
    if not test_imports():
        print("\n❌ Critical error: Import tests failed")
        print("\n🔧 Manual fix required:")
        print("pip uninstall Flask Jinja2 MarkupSafe Werkzeug -y")
        print("pip install Flask==2.2.5 Jinja2==3.0.3 MarkupSafe==2.1.1 Werkzeug==2.2.3")
        sys.exit(1)
    
    create_directories()
    
    if not test_web_application():
        print("\n❌ Web application test failed")
        print("🔧 Try running manually: python run_simple.py")
        sys.exit(1)
    
    print_header("INSTALLATION COMPLETED SUCCESSFULLY!")
    print("✅ All dependencies installed")
    print("✅ All imports working")
    print("✅ Web application ready")
    print("\n🚀 Starting the web application...")
    
    # Give user a moment to read
    time.sleep(2)
    
    # Start the application
    start_application()

if __name__ == "__main__":
    main()
