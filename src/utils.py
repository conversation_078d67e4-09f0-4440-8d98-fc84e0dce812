from pdf2image import convert_from_path
import os, json, re
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Helper: Extract PDF page as image
def extract_pages_from_pdf(pdf_file, page_number=None):
    file_name = os.path.basename(pdf_file)
    temp_images_dir = "./temp/images"
    images_folder = os.path.join(temp_images_dir, file_name)
    os.makedirs(images_folder, exist_ok=True)
    if page_number:
        images = convert_from_path(pdf_file, first_page=page_number, last_page=page_number, dpi=300)
        image_path = os.path.join(images_folder, f"page_{page_number}.jpg")
        images[0].save(image_path)
        return image_path
    else:
        images = convert_from_path(pdf_file, dpi=300)
        for i in range(len(images)):
            image_path = os.path.join(images_folder, f"page_{i+1}.jpg")
            images[i].save(image_path)
        return images

# Helper: Flatten nested JSON
def flatten_json(data, parent_key='', sep='_'):
    try:
        flat_dict = {}

        if isinstance(data, dict):
            for key, value in data.items():
                new_key = f"{parent_key}{sep}{key}" if parent_key else key

                if isinstance(value, dict):
                    flat_dict.update(flatten_json(value, new_key, sep=sep))
                elif isinstance(value, list):
                    if all(isinstance(i, dict) for i in value):
                        for idx, item in enumerate(value):
                            flat_dict.update(flatten_json(item, f"{new_key}{sep}{idx}", sep=sep))
                    else:
                        flat_dict[new_key] = "; ".join(map(str, value))
                else:
                    flat_dict[new_key] = value

        elif isinstance(data, list):
            for i, item in enumerate(data):
                flat_dict.update(flatten_json(item, parent_key=str(i), sep=sep))
        else:
            return None
        logger.info(f"Flattened dict: {flat_dict}")
        return flat_dict
    except Exception as e:
        logger.error(f"Error while flattening JSON: {e}")
        return None



def clean_vlm_output(output_text, model_type):
    # Clean and parse the output as JSON
    output_text = str(output_text)
    json_string = output_text.replace("\n", "").replace("json", "").replace("```","").replace("'", '')
    logger.info(f"Cleaned VLM output, {json_string}")
    if json_string:
        try:
            formatted_json = json.loads(json_string)
            logger.info(f"Loaded VLM output to a JSON")

            return formatted_json
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON, {e}")
            return None
    else:
        return None

def clean_qwen_output(text: str) -> str:
    # 1. Try to capture inside BEGIN..END with optional junk
    match = re.search(r'BEGIN##"?\s*\+?\s*"?({.*})"?\s*\+?\s*"?##END', text, re.DOTALL)
    if match:
        return match.group(1).strip()

    # 2. If not found, try raw braces { ... }
    match = re.search(r'(\{.*\})', text, re.DOTALL)
    if match:
        return match.group(1).strip()

    # 3. Fallback: return original
    return text.strip()

if __name__ == '__main__':
    # pdf_file = "/home/<USER>/secondry_drive/mohansatish/DE_SLS/2019226372.Owner_Policy.638657.pdf"
    # extract_pages_from_pdf(pdf_file)
    output_text =  '##" + " ##BEGIN##" + "{"borrower_information":[{"name":"MICHAEL JOSEPH","address":"1753 Barrow Ln, Brentwood, Tennessee 37027"},{"name":"NEVENE ANDRAWS","address":"1753 Barrow Ln, Brentwood, Tennessee 37027"}]}" + "##END##" {"borrower_information":[{"name":"MICHAEL JOSEPH","address":"1753 Barrow Ln, Brentwood, Tennessee 37027"},{"name":"NEVENE ANDRAWS","address":"1753 Barrow Ln, Brentwood, Tennessee 37027"}]} '
    model_type = "docvu.ai-v2"
    # res = clean_vlm_output(output_text, model_type)
    res = clean_qwen_output(output_text)
    print(res)
