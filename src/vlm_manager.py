from src.utils import flatten_json, clean_vlm_output
from docvu_vlm_demo.utils.loggers import logger
import pandas as pd

class VLMManager:
    def __init__(self, model_type=None):
        self.docvqavlm = None
        self.model_type = None
        if model_type:
            self.load(model_type)

    def load(self, model_type: str):
        # Unload previous
        self.unload()

        # Load new
        try:
            if model_type == "de-q2.5VL-3B":
                from docvu_vlm_demo.models import CDoCVuGPTClient
                self.docvqavlm = CDoCVuGPTClient()
            elif model_type == "ultra-de-vlm-v1":
                from docvu_vlm_demo.models.qwen_vision import CDoCVuQwenClient
                self.docvqavlm = CDoCVuQwenClient()
            elif model_type == "ultra-de-v1":
                from docvu_vlm_demo.models.qwen_text import CDoCVuQwenText
                self.docvqavlm = CDoCVuQwenText()
            else:
                raise ValueError(f"Unknown model type: {model_type}")
        except ImportError as e:
            logger.error(f"Failed to import model {model_type}: {e}")
            raise ImportError(f"Model {model_type} is not available. Please check dependencies.")

        self.model_type = model_type
        return self.docvqavlm

    def unload(self):
        if self.docvqavlm:
            try:
                if hasattr(self.docvqavlm, "model"):
                    self.docvqavlm.model.to("cpu")
                    del self.docvqavlm.model
            except Exception as e:
                print(f"⚠️ Error unloading model: {e}")
            del self.docvqavlm
            self.docvqavlm = None
            self.model_type = None

        import torch, gc
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.ipc_collect()
        gc.collect()
    
    @staticmethod
    def format_results_into_table(results):
        try:
            if isinstance(results, list):
                logger.info(f"No of records in the list:{len(results)}")
                logger.info(f"Final formatted results:{results}")
                if len(results)> 1:
                    result_df = pd.DataFrame(results)
                else:
                    result_df = pd.DataFrame(results, index=[0]).T.reset_index()
                    result_df.columns = ['Field', 'Value']

            elif isinstance(results, dict):
                result_df = pd.DataFrame(results, index=[0]).T.reset_index()
                result_df.columns = ['Field', 'Value']
            return result_df
        except Exception as e:
            logger.error(f"Error occured at converting to dataframe, {e}")
            return pd.DataFrame()

    def docvqa_function(self, prompt, image_path=None, input_format=None):
        dict_result = {'raw_vlm_output':[], 'formatted_vlm_output':[]}
        # modify prompt
        modified_prompt = prompt + ' return response in JSON format. Do not output any additional text." + " ##OUTPUT FORMAT: {"key1":"value1", "key2":"value2", "key3":"value3"}'
        if input_format:
            if input_format == 'table':
                modified_prompt = prompt + ' return response in JSON format. Do not output any additional text."+"##OUTPUT FORMAT: It should be list of dictionaries [{"key1":"value1", "key2":"value2"}, {"key1":"value3", "key2":"value4"}] '
            elif input_format == 'bullet-points':
                modified_prompt = prompt + " in list format. Do not output any additional text.  Assign the list to a key in a dictionary.return response in JSON format."
        if image_path:
            # inference on VLM
            vlm_output = self.docvqavlm.generate(image_path, modified_prompt)
        else:
            vlm_output = self.docvqavlm.text_generate(modified_prompt)
        dict_result['raw_vlm_output'] = vlm_output
        formatted_json = None
        # clean VLM output
        if vlm_output:
            formatted_json = clean_vlm_output(vlm_output, self.model_type)
        if formatted_json:
            if input_format == 'table':
                dict_result['formatted_vlm_output'] = formatted_json
                return dict_result
            dict_result['formatted_vlm_output'] = flatten_json(formatted_json)
            return dict_result
        else:
            return dict_result
    

if __name__ == '__main__':
    vlm_manager = VLMManager(model_type = 'ultra-de-vlm-v1')
    vlm_manager.unload()
