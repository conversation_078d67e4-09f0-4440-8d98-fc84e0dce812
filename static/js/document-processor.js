// Document Processing JavaScript for DocVu.AI

// Global variables - FIXED
let currentFile = null;
let currentFileType = null;
let isProcessing = false;

document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Document processor loading...');

    // Get DOM elements with error checking
    const fileInput = document.getElementById('fileInput');
    const textContent = document.getElementById('textContent');
    const submitBtn = document.getElementById('submitBtn');
    const submitStatus = document.getElementById('submitStatus');
    const inputModeRadios = document.querySelectorAll('input[name="inputMode"]');
    const fileUploadSection = document.getElementById('fileUploadSection');
    const textInputSection = document.getElementById('textInputSection');
    const pageNumberSection = document.getElementById('pageNumberSection');
    const examplePrompts = document.querySelectorAll('.example-prompt');
    const promptInput = document.getElementById('promptInput');
    const modelSelect = document.getElementById('modelSelect');
    const downloadExcel = document.getElementById('downloadExcel');
    const downloadJson = document.getElementById('downloadJson');
    const previewContainer = document.getElementById('previewContainer');

    // Debug: Log which elements are found
    console.log('🔍 Element check:');
    console.log('  submitBtn:', submitBtn ? '✅ Found' : '❌ NOT FOUND');
    console.log('  fileInput:', fileInput ? '✅ Found' : '❌ NOT FOUND');
    console.log('  textContent:', textContent ? '✅ Found' : '❌ NOT FOUND');
    console.log('  submitStatus:', submitStatus ? '✅ Found' : '❌ NOT FOUND');
    console.log('  previewContainer:', previewContainer ? '✅ Found' : '❌ NOT FOUND');
    console.log('  inputModeRadios:', inputModeRadios.length, 'found');

    // Check critical elements
    if (!submitBtn) {
        console.error('❌ CRITICAL: Submit button not found! ID: submitBtn');
        return;
    }

    if (!fileInput) {
        console.error('❌ CRITICAL: File input not found! ID: fileInput');
        return;
    }

    if (!submitStatus) {
        console.error('❌ CRITICAL: Submit status not found! ID: submitStatus');
        return;
    }

    if (!previewContainer) {
        console.error('❌ CRITICAL: Preview container not found! ID: previewContainer');
        return;
    }

    console.log('✅ All critical DOM elements found successfully');

    // Submit button update function - ENHANCED
    function updateSubmitButton() {
        console.log('🔄 updateSubmitButton() called');

        if (!submitBtn || !submitStatus) {
            console.error('❌ Submit button or status element not found in updateSubmitButton');
            return;
        }

        const inputModeRadio = document.querySelector('input[name="inputMode"]:checked');
        const inputMode = inputModeRadio ? inputModeRadio.value : 'file';
        const textValue = textContent ? textContent.value.trim() : '';

        let canProcess = false;
        let statusMessage = '';

        console.log('📊 Current state:', {
            inputMode,
            textLength: textValue.length,
            currentFile: currentFile ? currentFile.name : 'null',
            isProcessing
        });

        if (inputMode === 'text' && textValue) {
            canProcess = true;
            statusMessage = `Ready to process ${textValue.length} characters of text`;
            console.log('✅ Text mode: Ready to process');
        } else if (inputMode === 'file' && currentFile) {
            canProcess = true;
            statusMessage = `Ready to process file: ${currentFile.name}`;
            console.log('✅ File mode: Ready to process');
        } else if (inputMode === 'text') {
            statusMessage = 'Please enter text content to process';
            console.log('⏸️ Text mode: Waiting for text input');
        } else {
            statusMessage = 'Please upload a file to process';
            console.log('⏸️ File mode: Waiting for file upload');
        }

        // Update button state
        const shouldDisable = !canProcess || isProcessing;
        submitBtn.disabled = shouldDisable;
        submitStatus.textContent = statusMessage;

        // Update button appearance
        submitBtn.classList.remove('btn-secondary', 'btn-success', 'btn-warning');
        submitStatus.classList.remove('text-muted', 'text-success', 'text-warning');

        if (canProcess && !isProcessing) {
            submitBtn.innerHTML = '<i class="fas fa-play me-2"></i>Submit & Process Document';
            submitBtn.classList.add('btn-success');
            submitStatus.classList.add('text-success');
            console.log('🟢 Button state: SUCCESS (Green)');
        } else if (isProcessing) {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            submitBtn.classList.add('btn-warning');
            submitStatus.classList.add('text-warning');
            console.log('🟡 Button state: PROCESSING (Yellow)');
        } else {
            submitBtn.innerHTML = '<i class="fas fa-upload me-2"></i>Submit & Process Document';
            submitBtn.classList.add('btn-secondary');
            submitStatus.classList.add('text-muted');
            console.log('⚪ Button state: DISABLED (Gray)');
        }

        console.log('🎯 Final button state:', {
            disabled: submitBtn.disabled,
            canProcess,
            isProcessing,
            statusMessage
        });
    }

    // Document preview function - ENHANCED
    function showDocumentPreview(file, fileType) {
        console.log('🖼️ showDocumentPreview() called for:', file.name, 'Type:', fileType);

        if (!previewContainer) {
            console.error('❌ Preview container not found');
            return;
        }

        console.log('✅ Preview container found, updating content...');

        // Always show the preview container
        previewContainer.style.display = 'block';

        // Create a simple, reliable preview
        const fileSize = (file.size / 1024).toFixed(2);
        const uploadTime = new Date().toLocaleTimeString();

        if (fileType === 'pdf') {
            previewContainer.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-file-pdf text-danger me-2"></i>PDF File Uploaded</h6>
                    <p class="mb-1"><strong>File:</strong> ${file.name}</p>
                    <p class="mb-1"><strong>Size:</strong> ${fileSize} KB</p>
                    <p class="mb-1"><strong>Type:</strong> PDF Document</p>
                    <p class="mb-0"><strong>Uploaded:</strong> ${uploadTime}</p>
                </div>
                <div class="text-center text-muted">
                    <i class="fas fa-file-pdf fa-3x mb-2 text-danger"></i>
                    <p>PDF preview will be generated during processing</p>
                </div>
            `;
        } else if (fileType === 'image') {
            console.log('📸 Creating image preview...');
            previewContainer.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-image text-primary me-2"></i>Image File Uploaded</h6>
                    <p class="mb-1"><strong>File:</strong> ${file.name}</p>
                    <p class="mb-1"><strong>Size:</strong> ${fileSize} KB</p>
                    <p class="mb-0"><strong>Uploaded:</strong> ${uploadTime}</p>
                </div>
                <div class="text-center">
                    <div id="imagePreviewLoading">
                        <i class="fas fa-spinner fa-spin"></i> Loading image preview...
                    </div>
                </div>
            `;

            // Load image asynchronously
            const reader = new FileReader();
            reader.onload = function(e) {
                console.log('✅ Image loaded successfully');
                const imagePreview = `
                    <div class="text-center">
                        <img src="${e.target.result}" class="img-fluid border rounded" style="max-height: 300px; max-width: 100%;">
                    </div>
                `;
                // Replace just the loading part
                const loadingDiv = document.getElementById('imagePreviewLoading');
                if (loadingDiv) {
                    loadingDiv.outerHTML = imagePreview;
                }
            };
            reader.onerror = function() {
                console.error('❌ Failed to load image');
                const loadingDiv = document.getElementById('imagePreviewLoading');
                if (loadingDiv) {
                    loadingDiv.innerHTML = '<i class="fas fa-exclamation-triangle text-warning"></i> Failed to load image preview';
                }
            };
            reader.readAsDataURL(file);
        } else if (fileType === 'text') {
            console.log('📝 Creating text preview...');
            previewContainer.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-file-text text-success me-2"></i>Text File Uploaded</h6>
                    <p class="mb-1"><strong>File:</strong> ${file.name}</p>
                    <p class="mb-1"><strong>Size:</strong> ${fileSize} KB</p>
                    <p class="mb-0"><strong>Uploaded:</strong> ${uploadTime}</p>
                </div>
                <div id="textPreviewLoading">
                    <i class="fas fa-spinner fa-spin"></i> Loading text preview...
                </div>
            `;

            // Load text asynchronously
            const reader = new FileReader();
            reader.onload = function(e) {
                console.log('✅ Text loaded successfully');
                const content = e.target.result.substring(0, 500); // First 500 chars
                const textPreview = `
                    <div class="bg-light p-3 rounded">
                        <pre style="white-space: pre-wrap; font-size: 12px; max-height: 200px; overflow-y: auto;">${content}${e.target.result.length > 500 ? '\n\n... (truncated)' : ''}</pre>
                        <small class="text-muted">${e.target.result.length} characters total</small>
                    </div>
                `;
                const loadingDiv = document.getElementById('textPreviewLoading');
                if (loadingDiv) {
                    loadingDiv.outerHTML = textPreview;
                }
            };
            reader.onerror = function() {
                console.error('❌ Failed to load text');
                const loadingDiv = document.getElementById('textPreviewLoading');
                if (loadingDiv) {
                    loadingDiv.innerHTML = '<i class="fas fa-exclamation-triangle text-warning"></i> Failed to load text preview';
                }
            };
            reader.readAsText(file);
        } else {
            previewContainer.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-file text-secondary me-2"></i>File Uploaded</h6>
                    <p class="mb-1"><strong>File:</strong> ${file.name}</p>
                    <p class="mb-1"><strong>Size:</strong> ${fileSize} KB</p>
                    <p class="mb-1"><strong>Type:</strong> ${file.type || 'Unknown'}</p>
                    <p class="mb-0"><strong>Uploaded:</strong> ${uploadTime}</p>
                </div>
                <div class="text-center text-muted">
                    <i class="fas fa-file fa-3x mb-2"></i>
                    <p>Preview not available for this file type</p>
                </div>
            `;
        }

        console.log('✅ Preview updated successfully');
    }

    // Input mode change handler
    inputModeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            const mode = this.value;
            
            if (mode === 'file') {
                fileUploadSection.style.display = 'block';
                textInputSection.style.display = 'none';
            } else {
                fileUploadSection.style.display = 'none';
                textInputSection.style.display = 'block';
                pageNumberSection.style.display = 'none';
            }
            
            // Reset current file and update button
            currentFile = null;
            currentFileType = null;
            if (window.DocVuApp) {
                DocVuApp.resetResults();
            }
            updateSubmitButton();
            
            // Reset preview
            const previewContainer = document.getElementById('previewContainer');
            previewContainer.innerHTML = `
                <div class="text-center text-muted py-5">
                    <i class="fas fa-file-alt fa-3x mb-3"></i>
                    <p>${mode === 'file' ? 'Upload a document to see preview' : 'Text mode - no preview available'}</p>
                </div>
            `;
        });
    });

    // File input change handler - ENHANCED
    if (fileInput) {
        console.log('✅ Setting up file input event handler');
        fileInput.addEventListener('change', function(e) {
            console.log('📁 File input changed event triggered');
            const file = e.target.files[0];

            if (!file) {
                console.log('❌ No file selected, clearing state');
                currentFile = null;
                currentFileType = null;
                if (previewContainer) {
                    previewContainer.innerHTML = `
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-file-alt fa-3x mb-3"></i>
                            <p>Upload a document to see preview</p>
                        </div>
                    `;
                }
                updateSubmitButton();
                return;
            }

            console.log('📄 File selected:', {
                name: file.name,
                type: file.type,
                size: file.size,
                lastModified: new Date(file.lastModified).toLocaleString()
            });

            // Determine file type
            let fileType = 'unknown';
            if (file.type.includes('pdf')) {
                fileType = 'pdf';
            } else if (file.type.includes('image')) {
                fileType = 'image';
            } else if (file.type.includes('text') || file.name.toLowerCase().endsWith('.txt')) {
                fileType = 'text';
            }

            console.log('🏷️ Determined file type:', fileType);

            // Store file info
            currentFile = file;
            currentFileType = fileType;

            // Show preview immediately
            showDocumentPreview(file, fileType);

            // Update submit button
            updateSubmitButton();

            console.log('✅ File processing complete');

        try {
            // Validate file - FIXED
            if (window.DocVuApp && DocVuApp.validateFile) {
                DocVuApp.validateFile(file);
            } else {
                // Basic validation
                const maxSize = 50 * 1024 * 1024; // 50MB
                if (file.size > maxSize) {
                    throw new Error('File size exceeds 50MB limit');
                }
            }

            // Show loading
            if (window.DocVuApp && DocVuApp.showLoading) {
                DocVuApp.showLoading('Uploading file...', 'Please wait while we process your upload');
            }

            // Upload file - FIXED
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                currentFile = file;
                currentFileType = result.file_type;

                // Show page number input for PDFs
                if (result.file_type === 'pdf') {
                    pageNumberSection.style.display = 'block';
                } else {
                    pageNumberSection.style.display = 'none';
                }

                // Display preview - FIXED
                showDocumentPreview(file, result.file_type);

                // Update model options based on file type
                updateModelOptionsOld(result.file_type);

                // Show success message
                if (window.showAlert) {
                    showAlert(`✅ File "${result.filename}" uploaded successfully!`, 'success');
                } else {
                    console.log('File uploaded successfully:', result.filename);
                }

                // Update submit button
                updateSubmitButton();
            } else {
                throw new Error(result.error || 'Upload failed');
            }

        } catch (error) {
            console.error('Upload error:', error);
            if (window.showAlert) {
                showAlert(`❌ Upload failed: ${error.message}`, 'danger');
            } else {
                alert(`Upload failed: ${error.message}`);
            }
            fileInput.value = '';
            currentFile = null;
            currentFileType = null;
            if (previewContainer) {
                previewContainer.style.display = 'none';
            }
            updateSubmitButton();
        } finally {
            if (window.DocVuApp && DocVuApp.hideLoading) {
                DocVuApp.hideLoading();
            }
        }
    });

    // Text content change handler - FIXED
    if (textContent) {
        textContent.addEventListener('input', function() {
            console.log('Text content changed:', this.value.length, 'characters');
            updateSubmitButton();
        });
    } else {
        console.warn('Text content element not found');
    }

    // Helper functions - ADDED
    function showAlert(message, type = 'info') {
        // Simple alert implementation
        const alertContainer = document.getElementById('alertContainer') || document.body;
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        alertContainer.appendChild(alertDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }

    // Missing function - ADDED
    function updateModelOptionsOld(fileType) {
        console.log('Updating model options for file type:', fileType);
        // This function can be enhanced later for model-specific options
        if (modelSelect) {
            // Keep current model selection or set default
            console.log('Current model:', modelSelect.value);
        }
    }

    function displayPreview(previewData, fileType) {
        const previewContainer = document.getElementById('previewContainer');
        if (!previewContainer) return;

        if (fileType === 'pdf' && previewData && previewData.length > 0) {
            let previewHTML = '<div class="pdf-preview">';
            previewData.forEach(page => {
                previewHTML += `
                    <div class="mb-2">
                        <small class="text-muted">Page ${page.page}</small>
                        <img src="${page.image}" class="img-fluid border rounded" style="max-height: 200px;">
                    </div>
                `;
            });
            previewHTML += '</div>';
            previewContainer.innerHTML = previewHTML;
        } else if (fileType === 'image') {
            previewContainer.innerHTML = `
                <div class="text-center">
                    <img src="${URL.createObjectURL(currentFile)}" class="img-fluid border rounded" style="max-height: 300px;">
                </div>
            `;
        } else if (fileType === 'text') {
            previewContainer.innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="fas fa-file-text fa-2x mb-2"></i>
                    <p>Text file uploaded successfully</p>
                </div>
            `;
        } else {
            previewContainer.innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="fas fa-file fa-2x mb-2"></i>
                    <p>File uploaded successfully</p>
                </div>
            `;
        }
    }

    // Example prompt click handlers
    examplePrompts.forEach(button => {
        button.addEventListener('click', function() {
            const prompt = this.getAttribute('data-prompt');
            promptInput.value = prompt;
            
            // Add visual feedback
            this.classList.add('btn-primary');
            this.classList.remove('btn-outline-secondary');
            
            setTimeout(() => {
                this.classList.remove('btn-primary');
                this.classList.add('btn-outline-secondary');
            }, 1000);
        });
    });

    // Submit button click handler - FIXED
    if (submitBtn) {
        submitBtn.addEventListener('click', async function() {
            console.log('Submit button clicked');

            if (isProcessing) {
                console.log('Already processing, ignoring click');
                return;
            }

            const inputMode = document.querySelector('input[name="inputMode"]:checked')?.value || 'file';
            const prompt = promptInput ? promptInput.value.trim() : '';
            const inputFormat = document.getElementById('outputFormat')?.value || 'table';
            const modelType = modelSelect ? modelSelect.value : 'ultra-de-vlm-v1';
            const pageNumber = document.getElementById('pageNumber')?.value || '';

            console.log('Processing with:', { inputMode, prompt, inputFormat, modelType, pageNumber });

            if (!prompt) {
                if (window.showAlert) {
                    showAlert('⚠️ Please enter a prompt for extraction', 'warning');
                } else {
                    alert('Please enter a prompt for extraction');
                }
                return;
            }

        try {
            isProcessing = true;
            updateSubmitButton();
            
            // Upload text content if in text mode
            if (inputMode === 'text') {
                const textValue = textContent.value.trim();
                if (!textValue) {
                    throw new Error('Please enter some text content');
                }
                
                // Upload text
                const textResponse = await fetch('/upload_text', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ text_content: textValue })
                });

                if (!textResponse.ok) {
                    throw new Error('Failed to upload text');
                }
            }

            // Process document
            const processResponse = await fetch('/process', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    prompt: prompt,
                    input_format: inputFormat,
                    model_type: modelType,
                    page_number: pageNumber || null
                })
            });

            const result = await processResponse.json();

            if (result.success) {
                displayResults(result);
                showAlert('Document processed successfully!', 'success');
            } else {
                throw new Error(result.error || 'Processing failed');
            }

        } catch (error) {
            showAlert(error.message, 'danger');
        } finally {
            isProcessing = false;
            updateSubmitButton();
        }
    });

    // Download button handlers
    downloadExcel.addEventListener('click', function() {
        window.location.href = '/download/excel';
    });

    downloadJson.addEventListener('click', function() {
        window.location.href = '/download/json';
    });

    // Model selection change handler
    modelSelect.addEventListener('change', function() {
        const selectedModel = this.value;
        const inputMode = document.querySelector('input[name="inputMode"]:checked').value;
        
        // Show info about model capabilities
        if (selectedModel === 'ultra-de-v1' && inputMode === 'file' && currentFileType !== 'text') {
            DocVuApp.showAlert('Note: This model only supports text input. Please switch to text mode or select a vision-capable model.', 'info');
        }
    });

    // Helper functions
    function updateModelOptions(fileType) {
        const modelSelect = document.getElementById('modelSelect');
        const currentValue = modelSelect.value;
        
        // If text file, all models are available
        // If image/pdf, only vision models are available
        if (fileType === 'text') {
            // All models available
            Array.from(modelSelect.options).forEach(option => {
                option.disabled = false;
            });
        } else {
            // Only vision models available
            Array.from(modelSelect.options).forEach(option => {
                if (option.value === 'ultra-de-v1') {
                    option.disabled = true;
                } else {
                    option.disabled = false;
                }
            });
            
            // Switch to vision model if text-only model is selected
            if (currentValue === 'ultra-de-v1') {
                modelSelect.value = 'ultra-de-vlm-v1';
            }
        }
    }

    function displayResults(result) {
        // Update model selection based on file type
        if (fileType === 'text') {
            // For text files, prefer text-only model
            if (modelSelect.querySelector('option[value="ultra-de-v1"]')) {
                modelSelect.value = 'ultra-de-v1';
            }
        } else {
            // For PDF/images, prefer vision models
            if (modelSelect.querySelector('option[value="ultra-de-vlm-v1"]')) {
                modelSelect.value = 'ultra-de-vlm-v1';
            }
        }
    }

    function displayResults(result) {
        const resultsSection = document.getElementById('resultsSection');
        const processingTime = document.getElementById('processingTime');
        const tableHeaders = document.getElementById('tableHeaders');
        const tableBody = document.getElementById('tableBody');
        const rawOutput = document.getElementById('rawOutput');

        // Show results section
        resultsSection.style.display = 'block';
        resultsSection.scrollIntoView({ behavior: 'smooth' });

        // Display processing time
        processingTime.textContent = `⏱️ ${result.elapsed_time}s`;

        // Display structured results in TABLE FORMAT - ENHANCED
        if (result.structured_data && result.structured_data.length > 0) {
            // Create table headers with styling
            const headers = result.columns || Object.keys(result.structured_data[0]);
            tableHeaders.innerHTML = headers.map(header =>
                `<th class="bg-primary text-white border">${header.charAt(0).toUpperCase() + header.slice(1).replace(/_/g, ' ')}</th>`
            ).join('');

            // Create table rows with alternating colors
            tableBody.innerHTML = result.structured_data.map((row, index) => {
                const cells = headers.map(header => {
                    const value = row[header] || '';
                    return `<td class="border">${escapeHtml(String(value))}</td>`;
                }).join('');
                const rowClass = index % 2 === 0 ? 'table-light' : '';
                return `<tr class="${rowClass}">${cells}</tr>`;
            }).join('');

            // Show success message
            showAlert(`✅ Successfully extracted ${result.structured_data.length} records in table format!`, 'success');
        } else {
            // Fallback: try to display raw output as key-value table
            tableHeaders.innerHTML = '<th class="bg-warning text-dark border">Field</th><th class="bg-warning text-dark border">Value</th>';

            try {
                const rawData = JSON.parse(result.raw_output || '{}');
                const rows = Object.entries(rawData).map(([key, value], index) => {
                    const rowClass = index % 2 === 0 ? 'table-light' : '';
                    return `<tr class="${rowClass}">
                        <td class="border fw-bold">${escapeHtml(key.replace(/_/g, ' '))}</td>
                        <td class="border">${escapeHtml(String(value))}</td>
                    </tr>`;
                }).join('');

                tableBody.innerHTML = rows || '<tr><td colspan="2" class="text-muted text-center border">No data extracted</td></tr>';
            } catch (e) {
                tableBody.innerHTML = '<tr><td colspan="2" class="text-muted text-center border">Could not parse extracted data</td></tr>';
            }
        }

        // Display raw output
        rawOutput.textContent = result.raw_output || 'No raw output available';

        // Add animation and scroll to results
        resultsSection.classList.add('fade-in');
        setTimeout(() => {
            resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }, 100);
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Make variables available globally
    window.currentFile = null;
    window.currentFileType = null;
    window.isProcessing = false;

    // Input mode change handlers - FIXED
    if (inputModeRadios.length > 0) {
        inputModeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                console.log('Input mode changed to:', this.value);
                updateSubmitButton();
            });
        });
    }

    // Initialize submit button state
    console.log('Initializing submit button...');
    updateSubmitButton();

    console.log('Document processor initialization complete');
});
