// Main JavaScript for DocVu.AI VLM Web Application

// Global variables
let currentFile = null;
let currentFileType = null;
let isProcessing = false;

// Utility functions
function showAlert(message, type = 'info', duration = 5000) {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert-' + Date.now();
    
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" id="${alertId}" role="alert">
            <i class="fas fa-${getAlertIcon(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHTML);
    
    // Auto-dismiss after duration
    if (duration > 0) {
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, duration);
    }
}

function getAlertIcon(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle',
        'primary': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

function showLoading(text = 'Processing...', subtext = 'Please wait while we process your document') {
    document.getElementById('loadingText').textContent = text;
    document.getElementById('loadingSubtext').textContent = subtext;
    const modal = new bootstrap.Modal(document.getElementById('loadingModal'));
    modal.show();
}

function hideLoading() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('loadingModal'));
    if (modal) {
        modal.hide();
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function validateFile(file) {
    const maxSize = 50 * 1024 * 1024; // 50MB
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'text/plain'];
    
    if (file.size > maxSize) {
        throw new Error('File size exceeds 50MB limit');
    }
    
    if (!allowedTypes.includes(file.type)) {
        throw new Error('Unsupported file type. Please use PDF, JPG, PNG, or TXT files.');
    }
    
    return true;
}

function updateProcessButton() {
    const processBtn = document.getElementById('processBtn');
    const inputMode = document.querySelector('input[name="inputMode"]:checked').value;
    const textContent = document.getElementById('textContent').value.trim();
    const fileInput = document.getElementById('fileInput');
    
    let canProcess = false;
    
    if (inputMode === 'text' && textContent) {
        canProcess = true;
    } else if (inputMode === 'file' && currentFile) {
        canProcess = true;
    }
    
    processBtn.disabled = !canProcess || isProcessing;
    
    if (canProcess && !isProcessing) {
        processBtn.innerHTML = '<i class="fas fa-cogs me-2"></i>Process Document';
        processBtn.classList.remove('btn-secondary');
        processBtn.classList.add('btn-primary');
    } else {
        processBtn.innerHTML = '<i class="fas fa-upload me-2"></i>Upload Document First';
        processBtn.classList.remove('btn-primary');
        processBtn.classList.add('btn-secondary');
    }
}

function resetResults() {
    const resultsSection = document.getElementById('resultsSection');
    resultsSection.style.display = 'none';
}

function displayPreview(previewData, fileType) {
    const previewContainer = document.getElementById('previewContainer');
    
    if (fileType === 'pdf' && previewData) {
        let previewHTML = '';
        previewData.forEach(page => {
            previewHTML += `
                <div class="preview-page">
                    <h6>Page ${page.page}</h6>
                    <img src="${page.image}" class="preview-image" alt="Page ${page.page}">
                </div>
            `;
        });
        previewContainer.innerHTML = previewHTML;
    } else if (fileType === 'image') {
        previewContainer.innerHTML = `
            <div class="preview-page">
                <h6>Image Preview</h6>
                <img src="${URL.createObjectURL(currentFile)}" class="preview-image" alt="Uploaded Image">
            </div>
        `;
    } else if (fileType === 'text') {
        previewContainer.innerHTML = `
            <div class="preview-page">
                <h6>Text File Content</h6>
                <div class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 0.9rem;">
                    Loading text content...
                </div>
            </div>
        `;
        
        // Read text file content
        const reader = new FileReader();
        reader.onload = function(e) {
            const textContent = e.target.result.substring(0, 5000); // First 5000 chars
            const truncated = e.target.result.length > 5000 ? '...\n\n[Content truncated for preview]' : '';
            previewContainer.querySelector('.bg-light').textContent = textContent + truncated;
        };
        reader.readAsText(currentFile);
    } else {
        previewContainer.innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="fas fa-file-alt fa-3x mb-3"></i>
                <p>Preview not available for this file type</p>
            </div>
        `;
    }
}

// API functions
async function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch('/upload', {
        method: 'POST',
        body: formData
    });
    
    if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Upload failed');
    }
    
    return await response.json();
}

async function uploadText(textContent) {
    const formData = new FormData();
    formData.append('text_content', textContent);
    
    const response = await fetch('/upload', {
        method: 'POST',
        body: formData
    });
    
    if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Text upload failed');
    }
    
    return await response.json();
}

async function processDocument(prompt, inputFormat, modelType, pageNumber = null) {
    const response = await fetch('/process', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            prompt: prompt,
            input_format: inputFormat,
            model_type: modelType,
            page_number: pageNumber
        })
    });
    
    if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Processing failed');
    }
    
    return await response.json();
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Load available models
    fetch('/models')
        .then(response => response.json())
        .then(models => {
            const modelSelect = document.getElementById('modelSelect');
            modelSelect.innerHTML = '';
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model.value;
                option.textContent = model.name;
                modelSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading models:', error);
        });
    
    // Update process button state initially
    updateProcessButton();
});

// Export functions for use in other scripts
window.DocVuApp = {
    showAlert,
    hideLoading,
    showLoading,
    updateProcessButton,
    resetResults,
    displayPreview,
    uploadFile,
    uploadText,
    processDocument,
    validateFile,
    formatFileSize
};
