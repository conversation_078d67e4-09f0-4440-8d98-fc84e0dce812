// Complete Document Processing JavaScript - WORKING VERSION
console.log('🚀 Loading document processor...');

// Global variables
let currentFile = null;
let currentFileType = null;
let isProcessing = false;
let hasUploadedContent = false;

document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 DOM loaded, initializing...');

    // Get DOM elements with error checking
    const fileInput = document.getElementById('fileInput');
    const textContent = document.getElementById('textContent');
    const submitBtn = document.getElementById('submitBtn');
    const submitStatus = document.getElementById('submitStatus');
    const previewContainer = document.getElementById('previewContainer');
    const inputModeRadios = document.querySelectorAll('input[name="inputMode"]');
    const resultsSection = document.getElementById('resultsSection');
    const promptInput = document.getElementById('promptInput');
    const modelSelect = document.getElementById('modelSelect');
    const outputFormat = document.getElementById('outputFormat');

    // Debug: Log what we found
    console.log('🔍 Elements found:');
    console.log('  fileInput:', !!fileInput);
    console.log('  textContent:', !!textContent);
    console.log('  submitBtn:', !!submitBtn);
    console.log('  submitStatus:', !!submitStatus);
    console.log('  previewContainer:', !!previewContainer);
    console.log('  inputModeRadios:', inputModeRadios.length);
    console.log('  resultsSection:', !!resultsSection);

    // If critical elements missing, show error
    if (!submitBtn) {
        console.error('❌ CRITICAL: submitBtn not found!');
        showAlert('Error: Submit button not found in page', 'danger');
        return;
    }
    if (!fileInput) {
        console.error('❌ CRITICAL: fileInput not found!');
        showAlert('Error: File input not found in page', 'danger');
        return;
    }

    console.log('✅ Critical elements found, setting up handlers...');

    // Utility functions
    function showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alertContainer') || document.body;
        const alertId = 'alert-' + Date.now();

        const alertHTML = `
            <div class="alert alert-${type} alert-dismissible fade show" id="${alertId}" role="alert">
                <i class="fas fa-${getAlertIcon(type)} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        alertContainer.insertAdjacentHTML('beforeend', alertHTML);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    function getAlertIcon(type) {
        const icons = {
            'success': 'check-circle',
            'danger': 'exclamation-triangle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    function showLoading(show = true) {
        const loadingModal = document.getElementById('loadingModal');
        if (loadingModal) {
            if (show) {
                const modal = new bootstrap.Modal(loadingModal);
                modal.show();
            } else {
                const modal = bootstrap.Modal.getInstance(loadingModal);
                if (modal) modal.hide();
            }
        }
    }

    // Submit button update function
    function updateSubmitButton() {
        console.log('🔄 Updating submit button...');

        // Check current input mode
        const inputModeRadio = document.querySelector('input[name="inputMode"]:checked');
        const inputMode = inputModeRadio ? inputModeRadio.value : 'file';
        const textValue = textContent ? textContent.value.trim() : '';

        let canProcess = false;
        let statusMessage = '';

        // Determine if we can process
        if (inputMode === 'text' && textValue.length > 0) {
            canProcess = true;
            hasUploadedContent = true;
            statusMessage = `Ready to process ${textValue.length} characters of text`;
            console.log('✅ Text mode: Ready with', textValue.length, 'characters');
        } else if (inputMode === 'file' && currentFile) {
            canProcess = true;
            hasUploadedContent = true;
            statusMessage = `Ready to process file: ${currentFile.name}`;
            console.log('✅ File mode: Ready with file', currentFile.name);
        } else {
            hasUploadedContent = false;
            statusMessage = inputMode === 'text' ? 'Please enter text content' : 'Please upload a file';
            console.log('❌ Not ready:', statusMessage);
        }

        // Update button
        if (canProcess && !isProcessing) {
            submitBtn.disabled = false;
            submitBtn.className = 'btn btn-success btn-lg w-100';
            submitBtn.innerHTML = '<i class="fas fa-play me-2"></i>Submit & Process Document';
            console.log('🟢 Button enabled (GREEN)');
        } else if (isProcessing) {
            submitBtn.disabled = true;
            submitBtn.className = 'btn btn-warning btn-lg w-100';
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            console.log('🟡 Button processing (YELLOW)');
        } else {
            submitBtn.disabled = true;
            submitBtn.className = 'btn btn-secondary btn-lg w-100';
            submitBtn.innerHTML = '<i class="fas fa-upload me-2"></i>Submit & Process Document';
            console.log('⚪ Button disabled (GRAY)');
        }

        // Update status
        if (submitStatus) {
            submitStatus.textContent = statusMessage;
            submitStatus.className = canProcess ? 'text-success' : 'text-muted';
        }

        console.log('🎯 Update complete:', { canProcess, disabled: submitBtn.disabled });
    }



    // File upload handler
    async function uploadFile(file) {
        const formData = new FormData();
        formData.append('file', file);

        try {
            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (response.ok) {
                console.log('✅ File uploaded successfully:', result);
                showAlert(`File uploaded successfully: ${result.filename}`, 'success');
                return result;
            } else {
                console.error('❌ File upload failed:', result.error);
                showAlert(`Upload failed: ${result.error}`, 'danger');
                return null;
            }
        } catch (error) {
            console.error('❌ Upload error:', error);
            showAlert(`Upload error: ${error.message}`, 'danger');
            return null;
        }
    }

    // Text upload handler
    async function uploadText(textContent) {
        try {
            const response = await fetch('/upload_text', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ text_content: textContent })
            });

            const result = await response.json();

            if (response.ok) {
                console.log('✅ Text uploaded successfully:', result);
                showAlert(`Text uploaded successfully (${result.text_length} characters)`, 'success');
                return result;
            } else {
                console.error('❌ Text upload failed:', result.error);
                showAlert(`Text upload failed: ${result.error}`, 'danger');
                return null;
            }
        } catch (error) {
            console.error('❌ Text upload error:', error);
            showAlert(`Text upload error: ${error.message}`, 'danger');
            return null;
        }
    }

    // File input handler
    if (fileInput) {
        fileInput.addEventListener('change', async function(e) {
            console.log('📁 File input changed');
            const file = e.target.files[0];

            if (!file) {
                console.log('❌ No file selected');
                currentFile = null;
                currentFileType = null;
                if (previewContainer) {
                    previewContainer.innerHTML = `
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-file-alt fa-3x mb-3"></i>
                            <p>Upload a document to see preview</p>
                        </div>
                    `;
                }
                updateSubmitButton();
                return;
            }

            console.log('📄 File selected:', file.name, 'Size:', file.size, 'Type:', file.type);

            // Store file info
            currentFile = file;
            currentFileType = file.type.includes('pdf') ? 'pdf' :
                             file.type.includes('image') ? 'image' :
                             file.type.includes('text') ? 'text' : 'unknown';

            // Show loading preview
            if (previewContainer) {
                previewContainer.innerHTML = `
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Uploading...</span>
                        </div>
                        <p class="mt-2">Uploading file...</p>
                    </div>
                `;
            }

            // Upload file to server
            const uploadResult = await uploadFile(file);

            if (uploadResult) {
                // Show success preview
                if (previewContainer) {
                    const fileSize = (file.size / 1024).toFixed(2);
                    previewContainer.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>File Uploaded Successfully</h6>
                            <p class="mb-1"><strong>File:</strong> ${file.name}</p>
                            <p class="mb-1"><strong>Size:</strong> ${fileSize} KB</p>
                            <p class="mb-0"><strong>Type:</strong> ${uploadResult.file_type || currentFileType}</p>
                        </div>
                    `;
                }
            } else {
                // Show error preview
                if (previewContainer) {
                    previewContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Upload Failed</h6>
                            <p class="mb-0">Please try uploading the file again.</p>
                        </div>
                    `;
                }
                currentFile = null;
                currentFileType = null;
            }

            // Update submit button
            updateSubmitButton();
        });
        console.log('✅ File input handler attached');
    } else {
        console.error('❌ File input not found');
    }

    // Text input handler
    if (textContent) {
        textContent.addEventListener('input', async function() {
            console.log('📝 Text changed:', this.value.length, 'characters');

            const textValue = this.value.trim();
            if (textValue.length > 0) {
                // Auto-upload text content
                await uploadText(textValue);
            }

            updateSubmitButton();
        });
        console.log('✅ Text input handler attached');
    } else {
        console.error('❌ Text content not found');
    }

    // Input mode handlers
    inputModeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            console.log('🔄 Input mode changed to:', this.value);

            const fileUploadSection = document.getElementById('fileUploadSection');
            const textInputSection = document.getElementById('textInputSection');

            if (this.value === 'file') {
                if (fileUploadSection) fileUploadSection.style.display = 'block';
                if (textInputSection) textInputSection.style.display = 'none';
            } else {
                if (fileUploadSection) fileUploadSection.style.display = 'none';
                if (textInputSection) textInputSection.style.display = 'block';
            }

            updateSubmitButton();
        });
    });
    console.log('✅ Input mode handlers attached:', inputModeRadios.length);

    // Document processing handler
    async function processDocument() {
        if (isProcessing) {
            console.log('⏸️ Already processing, ignoring request');
            return;
        }

        if (!hasUploadedContent) {
            showAlert('Please upload a file or enter text content first', 'warning');
            return;
        }

        isProcessing = true;
        updateSubmitButton();
        showLoading(true);

        try {
            const prompt = promptInput ? promptInput.value.trim() : 'Extract important information.';
            const model = modelSelect ? modelSelect.value : 'ultra-de-vlm-v1';
            const format = outputFormat ? outputFormat.value : 'key-value';
            const pageNumber = document.getElementById('pageNumber')?.value || null;

            console.log('🚀 Processing document with:', { prompt, model, format, pageNumber });

            const response = await fetch('/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    prompt: prompt,
                    model_type: model,
                    input_format: format,
                    page_number: pageNumber
                })
            });

            const result = await response.json();

            if (response.ok) {
                console.log('✅ Processing successful:', result);
                showAlert(`Processing completed in ${result.elapsed_time}s`, 'success');
                displayResults(result);
            } else {
                console.error('❌ Processing failed:', result.error);
                showAlert(`Processing failed: ${result.error}`, 'danger');
            }

        } catch (error) {
            console.error('❌ Processing error:', error);
            showAlert(`Processing error: ${error.message}`, 'danger');
        } finally {
            isProcessing = false;
            updateSubmitButton();
            showLoading(false);
        }
    }

    // Display results function
    function displayResults(result) {
        if (!resultsSection) {
            console.error('❌ Results section not found');
            return;
        }

        // Show results section
        resultsSection.style.display = 'block';

        // Update processing time
        const processingTime = document.getElementById('processingTime');
        if (processingTime) {
            processingTime.textContent = `${result.elapsed_time}s`;
        }

        // Display structured data in table
        const tableHeaders = document.getElementById('tableHeaders');
        const tableBody = document.getElementById('tableBody');

        if (tableHeaders && tableBody && result.structured_data && result.structured_data.length > 0) {
            // Clear existing content
            tableHeaders.innerHTML = '';
            tableBody.innerHTML = '';

            // Create headers
            const columns = result.columns || Object.keys(result.structured_data[0]);
            columns.forEach(column => {
                const th = document.createElement('th');
                th.textContent = column;
                tableHeaders.appendChild(th);
            });

            // Create rows
            result.structured_data.forEach(row => {
                const tr = document.createElement('tr');
                columns.forEach(column => {
                    const td = document.createElement('td');
                    td.textContent = row[column] || '';
                    tr.appendChild(td);
                });
                tableBody.appendChild(tr);
            });
        }

        // Display raw output
        const rawOutput = document.getElementById('rawOutput');
        if (rawOutput) {
            rawOutput.textContent = result.raw_output || 'No raw output available';
        }

        // Scroll to results
        resultsSection.scrollIntoView({ behavior: 'smooth' });
    }

    // Submit button handler
    if (submitBtn) {
        submitBtn.addEventListener('click', function() {
            console.log('🚀 Submit button clicked!');
            processDocument();
        });
        console.log('✅ Submit button handler attached');
    } else {
        console.error('❌ Submit button handler failed - button not found');
    }

    // Download handlers
    const downloadExcel = document.getElementById('downloadExcel');
    const downloadJson = document.getElementById('downloadJson');

    if (downloadExcel) {
        downloadExcel.addEventListener('click', function() {
            window.open('/download/excel', '_blank');
        });
    }

    if (downloadJson) {
        downloadJson.addEventListener('click', function() {
            window.open('/download/json', '_blank');
        });
    }

    // Example prompt handlers
    const examplePrompts = document.querySelectorAll('.example-prompt');
    examplePrompts.forEach(button => {
        button.addEventListener('click', function() {
            const prompt = this.getAttribute('data-prompt');
            if (promptInput && prompt) {
                promptInput.value = prompt;
                console.log('📝 Example prompt selected:', prompt);
            }
        });
    });

    // Initialize everything
    console.log('🔧 Initializing submit button state...');
    updateSubmitButton();
    console.log('✅ Document processor ready!');
});
