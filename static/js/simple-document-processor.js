// Simple Document Processing JavaScript - ULTRA MINIMAL VERSION
console.log('🚀 Loading simple document processor...');

// Global variables
let currentFile = null;
let currentFileType = null;
let isProcessing = false;

document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 DOM loaded, initializing...');

    // Get DOM elements with error checking
    const fileInput = document.getElementById('fileInput');
    const textContent = document.getElementById('textContent');
    const submitBtn = document.getElementById('submitBtn');
    const submitStatus = document.getElementById('submitStatus');
    const previewContainer = document.getElementById('previewContainer');
    const inputModeRadios = document.querySelectorAll('input[name="inputMode"]');

    // Debug: Log what we found
    console.log('🔍 Elements found:');
    console.log('  fileInput:', !!fileInput);
    console.log('  textContent:', !!textContent);
    console.log('  submitBtn:', !!submitBtn);
    console.log('  submitStatus:', !!submitStatus);
    console.log('  previewContainer:', !!previewContainer);
    console.log('  inputModeRadios:', inputModeRadios.length);

    // If critical elements missing, show error
    if (!submitBtn) {
        console.error('❌ CRITICAL: submitBtn not found!');
        alert('Error: Submit button not found in page');
        return;
    }
    if (!fileInput) {
        console.error('❌ CRITICAL: fileInput not found!');
        alert('Error: File input not found in page');
        return;
    }

    console.log('✅ Critical elements found, setting up handlers...');

    // Simple submit button update function
    function updateSubmitButton() {
        console.log('🔄 Updating submit button...');

        // Check current input mode
        const inputModeRadio = document.querySelector('input[name="inputMode"]:checked');
        const inputMode = inputModeRadio ? inputModeRadio.value : 'file';
        const textValue = textContent ? textContent.value.trim() : '';

        let canProcess = false;
        let statusMessage = '';

        // Determine if we can process
        if (inputMode === 'text' && textValue.length > 0) {
            canProcess = true;
            statusMessage = `Ready to process ${textValue.length} characters of text`;
            console.log('✅ Text mode: Ready with', textValue.length, 'characters');
        } else if (inputMode === 'file' && currentFile) {
            canProcess = true;
            statusMessage = `Ready to process file: ${currentFile.name}`;
            console.log('✅ File mode: Ready with file', currentFile.name);
        } else {
            statusMessage = inputMode === 'text' ? 'Please enter text content' : 'Please upload a file';
            console.log('❌ Not ready:', statusMessage);
        }

        // Update button
        if (canProcess && !isProcessing) {
            submitBtn.disabled = false;
            submitBtn.className = 'btn btn-success btn-lg w-100';
            submitBtn.innerHTML = '<i class="fas fa-play me-2"></i>Submit & Process Document';
            console.log('🟢 Button enabled (GREEN)');
        } else {
            submitBtn.disabled = true;
            submitBtn.className = 'btn btn-secondary btn-lg w-100';
            submitBtn.innerHTML = '<i class="fas fa-upload me-2"></i>Submit & Process Document';
            console.log('⚪ Button disabled (GRAY)');
        }

        // Update status
        if (submitStatus) {
            submitStatus.textContent = statusMessage;
            submitStatus.className = canProcess ? 'text-success' : 'text-muted';
        }

        console.log('🎯 Update complete:', { canProcess, disabled: submitBtn.disabled });
    }



    // Simple file input handler
    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            console.log('📁 File input changed');
            const file = e.target.files[0];

            if (!file) {
                console.log('❌ No file selected');
                currentFile = null;
                currentFileType = null;
                if (previewContainer) {
                    previewContainer.innerHTML = `
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-file-alt fa-3x mb-3"></i>
                            <p>Upload a document to see preview</p>
                        </div>
                    `;
                }
                updateSubmitButton();
                return;
            }

            console.log('📄 File selected:', file.name, 'Size:', file.size, 'Type:', file.type);

            // Store file info
            currentFile = file;
            currentFileType = file.type.includes('pdf') ? 'pdf' :
                             file.type.includes('image') ? 'image' :
                             file.type.includes('text') ? 'text' : 'unknown';

            // Show simple preview
            if (previewContainer) {
                const fileSize = (file.size / 1024).toFixed(2);
                previewContainer.innerHTML = `
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>File Uploaded Successfully</h6>
                        <p class="mb-1"><strong>File:</strong> ${file.name}</p>
                        <p class="mb-1"><strong>Size:</strong> ${fileSize} KB</p>
                        <p class="mb-0"><strong>Type:</strong> ${currentFileType}</p>
                    </div>
                `;
                console.log('✅ Preview updated');
            }

            // Update submit button
            updateSubmitButton();
        });
        console.log('✅ File input handler attached');
    } else {
        console.error('❌ File input not found');
    }

    // Simple text input handler
    if (textContent) {
        textContent.addEventListener('input', function() {
            console.log('📝 Text changed:', this.value.length, 'characters');
            updateSubmitButton();
        });
        console.log('✅ Text input handler attached');
    } else {
        console.error('❌ Text content not found');
    }

    // Simple input mode handlers
    inputModeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            console.log('🔄 Input mode changed to:', this.value);

            const fileUploadSection = document.getElementById('fileUploadSection');
            const textInputSection = document.getElementById('textInputSection');

            if (this.value === 'file') {
                if (fileUploadSection) fileUploadSection.style.display = 'block';
                if (textInputSection) textInputSection.style.display = 'none';
            } else {
                if (fileUploadSection) fileUploadSection.style.display = 'none';
                if (textInputSection) textInputSection.style.display = 'block';
            }

            updateSubmitButton();
        });
    });
    console.log('✅ Input mode handlers attached:', inputModeRadios.length);

    // Simple submit button handler
    if (submitBtn) {
        submitBtn.addEventListener('click', function() {
            console.log('🚀 Submit button clicked!');

            if (isProcessing) {
                console.log('⏸️ Already processing, ignoring click');
                return;
            }

            const inputMode = document.querySelector('input[name="inputMode"]:checked')?.value || 'file';
            console.log('📋 Processing mode:', inputMode);

            // Simple success simulation
            alert('✅ Submit button working!\n\nMode: ' + inputMode + '\nFile: ' + (currentFile ? currentFile.name : 'none'));
        });
        console.log('✅ Submit button handler attached');
    } else {
        console.error('❌ Submit button handler failed - button not found');
    }

    // Initialize everything
    console.log('🔧 Initializing submit button state...');
    updateSubmitButton();
    console.log('✅ Simple document processor ready!');
});
