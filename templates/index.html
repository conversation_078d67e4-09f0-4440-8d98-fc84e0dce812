{% extends "base.html" %}

{% block content %}
<div class="row">
    <!-- Left Panel - Controls -->
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-upload me-2"></i>
                    Document Upload & Processing
                </h4>
            </div>
            <div class="card-body">
                <!-- Input Mode Selection -->
                <div class="mb-4">
                    <label class="form-label fw-bold">Input Mode:</label>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="inputMode" id="fileMode" value="file" checked>
                        <label class="btn btn-outline-primary" for="fileMode">
                            <i class="fas fa-file me-1"></i>Upload File
                        </label>
                        <input type="radio" class="btn-check" name="inputMode" id="textMode" value="text">
                        <label class="btn btn-outline-primary" for="textMode">
                            <i class="fas fa-keyboard me-1"></i>Paste Text
                        </label>
                    </div>
                </div>

                <!-- File Upload Section -->
                <div id="fileUploadSection">
                    <div class="mb-3">
                        <label for="fileInput" class="form-label fw-bold">Choose File:</label>
                        <input type="file" class="form-control" id="fileInput" 
                               accept=".pdf,.jpg,.jpeg,.png,.txt">
                        <div class="form-text">
                            Supported formats: PDF, JPG, PNG, TXT (Max size: 50MB)
                        </div>
                    </div>
                </div>

                <!-- Text Input Section -->
                <div id="textInputSection" style="display: none;">
                    <div class="mb-3">
                        <label for="textContent" class="form-label fw-bold">Paste Document Text:</label>
                        <textarea class="form-control" id="textContent" rows="8" 
                                  placeholder="Paste your document text here..."></textarea>
                    </div>
                </div>

                <!-- Processing Options -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="modelSelect" class="form-label fw-bold">Model:</label>
                        <select class="form-select" id="modelSelect">
                            <option value="ultra-de-vlm-v1">Ultra DE VLM v1 (Vision + Text)</option>
                            <option value="de-q2.5VL-3B">Qwen2.5-VL 3B (Vision + Text)</option>
                            <option value="ultra-de-v1">Ultra DE v1 (Text Only)</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="outputFormat" class="form-label fw-bold">Output Format:</label>
                        <select class="form-select" id="outputFormat">
                            <option value="key-value">Key-Value Pairs</option>
                            <option value="table">Table Format</option>
                            <option value="bullet-points">Bullet Points</option>
                        </select>
                    </div>
                </div>

                <!-- Page Number (for PDFs) -->
                <div id="pageNumberSection" class="mb-3" style="display: none;">
                    <label for="pageNumber" class="form-label fw-bold">Page Number (Optional):</label>
                    <input type="number" class="form-control" id="pageNumber" min="1"
                           placeholder="Leave empty for auto-selection using RAG">
                    <div class="form-text">
                        If not specified, RAG will automatically select the most relevant page
                    </div>
                </div>

                <!-- Prompt Input -->
                <div class="mb-3">
                    <label for="promptInput" class="form-label fw-bold">Extraction Prompt:</label>
                    <input type="text" class="form-control" id="promptInput"
                           value="Extract important information."
                           placeholder="Enter your extraction prompt...">
                </div>

                <!-- Submit Button - RIGHT AFTER PROMPT -->
                <div class="mb-4">
                    <button type="button" class="btn btn-success btn-lg w-100" id="submitBtn" disabled>
                        <i class="fas fa-play me-2"></i>Submit & Process Document
                    </button>
                    <div class="form-text text-center mt-2">
                        <small id="submitStatus" class="text-muted">Please upload a file or enter text to enable processing</small>
                    </div>
                </div>

                <!-- Example Prompts -->
                <div class="mb-4">
                    <label class="form-label fw-bold">Example Prompts:</label>
                    <div class="d-flex flex-wrap gap-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary example-prompt" 
                                data-prompt="Extract borrower information and co borrower information from section 3 only.">
                            Borrower Info
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary example-prompt" 
                                data-prompt="Extract mortgage information section only.">
                            Mortgage Info
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary example-prompt" 
                                data-prompt="Extract all financial data including income, assets, and liabilities.">
                            Financial Data
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary example-prompt" 
                                data-prompt="Extract property details and address information.">
                            Property Details
                        </button>
                    </div>
                </div>

                <!-- Old Process Button Removed - Now using Submit Button after prompt -->
            </div>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="mt-4" style="display: none;">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        Extraction Results
                    </h5>
                    <span id="processingTime" class="badge bg-light text-dark"></span>
                </div>
                <div class="card-body">
                    <!-- Download Buttons -->
                    <div class="mb-3">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary" id="downloadExcel">
                                <i class="fas fa-file-excel me-1"></i>Download Excel
                            </button>
                            <button type="button" class="btn btn-outline-primary" id="downloadJson">
                                <i class="fas fa-file-code me-1"></i>Download JSON
                            </button>
                        </div>
                    </div>

                    <!-- Structured Results -->
                    <div class="mb-4">
                        <h6 class="fw-bold">Structured Results:</h6>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="resultsTable">
                                <thead class="table-dark">
                                    <tr id="tableHeaders"></tr>
                                </thead>
                                <tbody id="tableBody"></tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Raw Results -->
                    <div>
                        <h6 class="fw-bold">Raw Output:</h6>
                        <pre class="bg-light p-3 rounded" id="rawOutput" style="max-height: 300px; overflow-y: auto;"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Right Panel - Preview -->
    <div class="col-lg-4">
        <div class="card shadow-sm">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-eye me-2"></i>
                    Document Preview
                </h5>
            </div>
            <div class="card-body">
                <div id="previewContainer">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-file-alt fa-3x mb-3"></i>
                        <p>Upload a document to see preview</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="{{ url_for('static', filename='js/simple-document-processor.js') }}"></script>
{% endblock %}
