#!/usr/bin/env python3
"""
Basic functionality test for VLM Web Application
Tests core components without requiring heavy ML models
"""

import sys
import os
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_imports():
    """Test if core modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        from flask import Flask
        print("   ✅ Flask imported successfully")
    except ImportError as e:
        print(f"   ❌ Flask import failed: {e}")
        return False
    
    try:
        import pandas as pd
        print("   ✅ Pandas imported successfully")
    except ImportError as e:
        print(f"   ❌ Pandas import failed: {e}")
        return False
    
    try:
        from PIL import Image
        print("   ✅ PIL imported successfully")
    except ImportError as e:
        print(f"   ❌ PIL import failed: {e}")
        return False
    
    try:
        from src.utils import flatten_json, clean_vlm_output
        print("   ✅ Utils imported successfully")
    except ImportError as e:
        print(f"   ❌ Utils import failed: {e}")
        return False
    
    return True

def test_utils():
    """Test utility functions"""
    print("\n🛠️  Testing utility functions...")
    
    try:
        from src.utils import flatten_json, clean_vlm_output
        
        # Test flatten_json
        test_data = {"a": {"b": {"c": "value"}}, "d": [1, 2, 3]}
        result = flatten_json(test_data)
        if result and "a_b_c" in result:
            print("   ✅ flatten_json works correctly")
        else:
            print("   ❌ flatten_json failed")
            return False
        
        # Test clean_vlm_output
        test_output = '```json\n{"key": "value"}\n```'
        result = clean_vlm_output(test_output, "test")
        if result and isinstance(result, dict):
            print("   ✅ clean_vlm_output works correctly")
        else:
            print("   ❌ clean_vlm_output failed")
            return False
        
        return True
    except Exception as e:
        print(f"   ❌ Utils test failed: {e}")
        return False

def test_flask_app():
    """Test Flask app creation"""
    print("\n🌐 Testing Flask app...")
    
    try:
        from app import app
        
        # Test app creation
        if app:
            print("   ✅ Flask app created successfully")
        else:
            print("   ❌ Flask app creation failed")
            return False
        
        # Test routes
        with app.test_client() as client:
            response = client.get('/')
            if response.status_code == 200:
                print("   ✅ Main route works")
            else:
                print(f"   ❌ Main route failed: {response.status_code}")
                return False
            
            response = client.get('/models')
            if response.status_code == 200:
                print("   ✅ Models endpoint works")
            else:
                print(f"   ❌ Models endpoint failed: {response.status_code}")
                return False
        
        return True
    except Exception as e:
        print(f"   ❌ Flask app test failed: {e}")
        return False

def test_directories():
    """Test directory structure"""
    print("\n📁 Testing directory structure...")
    
    required_dirs = [
        "templates",
        "static/css", 
        "static/js",
        "src",
        "docvu_vlm_demo/models"
    ]
    
    all_good = True
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"   ✅ {dir_path} exists")
        else:
            print(f"   ❌ {dir_path} missing")
            all_good = False
    
    return all_good

def test_config():
    """Test configuration"""
    print("\n⚙️  Testing configuration...")
    
    try:
        from config import Config, DevelopmentConfig
        
        config = DevelopmentConfig()
        if hasattr(config, 'SECRET_KEY'):
            print("   ✅ Configuration loaded successfully")
        else:
            print("   ❌ Configuration missing required attributes")
            return False
        
        return True
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
        return False

def main():
    """Run all basic tests"""
    print("🧪 VLM Web Application - Basic Functionality Test")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Utils Test", test_utils),
        ("Directory Test", test_directories),
        ("Config Test", test_config),
        ("Flask App Test", test_flask_app)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"   ❌ {test_name} failed with error: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"   🎉 All basic tests passed! ({passed}/{total})")
        print("   ✅ Core functionality is working")
        print("\n🚀 Ready to start the application!")
        print("   Run: python run_web_app.py")
    else:
        print(f"   ⚠️  {passed}/{total} tests passed")
        print("   ❌ Some basic functionality issues found")
        print("\n🔧 Try running the quick setup:")
        print("   python quick_setup.py")
    
    print("=" * 60)
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
