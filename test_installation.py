#!/usr/bin/env python3
"""
Test script to verify VLM Web Application installation
"""

import sys
import os
import importlib
from pathlib import Path

def test_python_version():
    """Test Python version compatibility"""
    print("🐍 Testing Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"   ✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"   ❌ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.8+")
        return False

def test_required_packages():
    """Test if required packages are installed"""
    print("\n📦 Testing required packages...")
    
    required_packages = [
        'flask',
        'pandas',
        'numpy',
        'PIL',
        'pdf2image',
        'torch'
    ]
    
    optional_packages = [
        'transformers',
        'openai',
        'byaldi'
    ]
    
    all_good = True
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"   ✅ {package} - Installed")
        except ImportError:
            print(f"   ❌ {package} - Missing (Required)")
            all_good = False
    
    print("\n   Optional packages:")
    for package in optional_packages:
        try:
            importlib.import_module(package)
            print(f"   ✅ {package} - Installed")
        except ImportError:
            print(f"   ⚠️  {package} - Missing (Optional)")
    
    return all_good

def test_directories():
    """Test if required directories exist"""
    print("\n📁 Testing directory structure...")
    
    required_dirs = [
        'templates',
        'static',
        'static/css',
        'static/js',
        'src',
        'docvu_vlm_demo',
        'docvu_vlm_demo/models'
    ]
    
    all_good = True
    
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"   ✅ {dir_path} - Exists")
        else:
            print(f"   ❌ {dir_path} - Missing")
            all_good = False
    
    return all_good

def test_files():
    """Test if required files exist"""
    print("\n📄 Testing required files...")
    
    required_files = [
        'app.py',
        'config.py',
        'requirements.txt',
        'run_web_app.py',
        'templates/index.html',
        'templates/base.html',
        'static/css/style.css',
        'static/js/main.js',
        'src/vlm_manager.py',
        'src/utils.py'
    ]
    
    all_good = True
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path} - Exists")
        else:
            print(f"   ❌ {file_path} - Missing")
            all_good = False
    
    return all_good

def test_gpu_availability():
    """Test GPU availability"""
    print("\n🎮 Testing GPU availability...")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"   ✅ GPU Available: {gpu_name} ({gpu_count} device(s))")
            return True
        else:
            print("   ⚠️  No GPU available - Will use CPU (slower)")
            return False
    except ImportError:
        print("   ❌ PyTorch not installed - Cannot check GPU")
        return False

def test_app_import():
    """Test if the Flask app can be imported"""
    print("\n🌐 Testing Flask app import...")
    
    try:
        # Add current directory to path
        sys.path.insert(0, str(Path.cwd()))
        
        from app import app
        print("   ✅ Flask app imported successfully")
        
        # Test basic configuration
        if hasattr(app, 'config'):
            print("   ✅ App configuration loaded")
        
        return True
    except Exception as e:
        print(f"   ❌ Failed to import Flask app: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 DocVu.AI VLM Web Application - Installation Test")
    print("=" * 60)
    
    tests = [
        test_python_version,
        test_required_packages,
        test_directories,
        test_files,
        test_gpu_availability,
        test_app_import
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"   ❌ Test failed with error: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"   🎉 All tests passed! ({passed}/{total})")
        print("   ✅ Installation appears to be complete and working")
        print("\n🚀 You can now start the application with:")
        print("   python run_web_app.py")
        print("   or")
        print("   ./run_web_app.sh")
    else:
        print(f"   ⚠️  {passed}/{total} tests passed")
        print("   ❌ Some issues found - please check the output above")
        print("\n🔧 To fix issues:")
        print("   1. Install missing packages: pip install -r requirements.txt")
        print("   2. Check file permissions and directory structure")
        print("   3. Verify Python version compatibility")
    
    print("=" * 60)
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
