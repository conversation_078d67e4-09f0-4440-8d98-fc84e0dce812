<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Submit Button Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Submit Button & Preview Test</h1>
        
        <!-- Input Mode Selection -->
        <div class="mb-3">
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" name="inputMode" id="fileMode" value="file" checked>
                <label class="form-check-label" for="fileMode">File Upload</label>
            </div>
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" name="inputMode" id="textMode" value="text">
                <label class="form-check-label" for="textMode">Text Input</label>
            </div>
        </div>

        <!-- File Upload Section -->
        <div id="fileUploadSection">
            <div class="mb-3">
                <label for="fileInput" class="form-label fw-bold">Choose File:</label>
                <input type="file" class="form-control" id="fileInput" 
                       accept=".pdf,.jpg,.jpeg,.png,.txt">
            </div>
        </div>

        <!-- Text Input Section -->
        <div id="textInputSection" style="display: none;">
            <div class="mb-3">
                <label for="textContent" class="form-label fw-bold">Text Content:</label>
                <textarea class="form-control" id="textContent" rows="6" 
                          placeholder="Paste or type your text content here..."></textarea>
            </div>
        </div>

        <!-- Page Number Section (for PDFs) -->
        <div id="pageNumberSection" style="display: none;">
            <div class="mb-3">
                <label for="pageNumber" class="form-label fw-bold">Page Number (optional):</label>
                <input type="number" class="form-control" id="pageNumber" min="1" 
                       placeholder="Leave empty to process all pages">
            </div>
        </div>

        <!-- Prompt Input -->
        <div class="mb-3">
            <label for="promptInput" class="form-label fw-bold">Extraction Prompt:</label>
            <textarea class="form-control" id="promptInput" rows="3" 
                      placeholder="Enter your extraction prompt...">Extract all text content</textarea>
        </div>

        <!-- Submit Button -->
        <div class="mb-4">
            <button type="button" class="btn btn-success btn-lg w-100" id="submitBtn" disabled>
                <i class="fas fa-play me-2"></i>Submit & Process Document
            </button>
            <div class="form-text text-center mt-2">
                <small id="submitStatus" class="text-muted">Please upload a file or enter text to enable processing</small>
            </div>
        </div>

        <!-- Model Selection -->
        <div class="mb-3">
            <label for="modelSelect" class="form-label fw-bold">AI Model:</label>
            <select class="form-select" id="modelSelect">
                <option value="qwen2-vl">Qwen2-VL-7B-Instruct (Local)</option>
                <option value="gpt-4v">GPT-4V (API)</option>
                <option value="colpali">ColPali RAG (Local)</option>
            </select>
        </div>

        <!-- Document Preview -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-eye me-2"></i>
                    Document Preview
                </h5>
            </div>
            <div class="card-body">
                <div id="previewContainer">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-file-alt fa-3x mb-3"></i>
                        <p>Upload a document to see preview</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="static/js/simple-document-processor.js"></script>
</body>
</html>
