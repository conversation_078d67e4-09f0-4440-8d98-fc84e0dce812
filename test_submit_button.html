<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Submit Button Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Submit Button & Preview Test</h2>
        
        <!-- Input Mode Selection -->
        <div class="mb-3">
            <label class="form-label fw-bold">Input Mode:</label>
            <div>
                <input type="radio" name="inputMode" value="file" id="fileMode" checked>
                <label for="fileMode">File Upload</label>
                
                <input type="radio" name="inputMode" value="text" id="textMode" class="ms-3">
                <label for="textMode">Text Input</label>
            </div>
        </div>
        
        <!-- File Upload Section -->
        <div id="fileUploadSection" class="mb-3">
            <label for="fileInput" class="form-label fw-bold">Upload Document:</label>
            <input type="file" class="form-control" id="fileInput" accept=".pdf,.png,.jpg,.jpeg,.txt">
        </div>
        
        <!-- Text Input Section -->
        <div id="textInputSection" class="mb-3" style="display: none;">
            <label for="textContent" class="form-label fw-bold">Text Content:</label>
            <textarea class="form-control" id="textContent" rows="5" placeholder="Paste your text content here..."></textarea>
        </div>
        
        <!-- Prompt Input -->
        <div class="mb-3">
            <label for="promptInput" class="form-label fw-bold">Extraction Prompt:</label>
            <input type="text" class="form-control" id="promptInput" value="Extract important information." placeholder="Enter your extraction prompt...">
        </div>
        
        <!-- Submit Button -->
        <div class="mb-4">
            <button type="button" class="btn btn-success btn-lg w-100" id="submitBtn" disabled>
                <i class="fas fa-play me-2"></i>Submit & Process Document
            </button>
            <div class="form-text text-center mt-2">
                <small id="submitStatus" class="text-muted">Please upload a file or enter text to enable processing</small>
            </div>
        </div>
        
        <!-- Preview Container -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-eye me-2"></i>Document Preview</h5>
                    </div>
                    <div class="card-body">
                        <div id="previewContainer">
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-file-alt fa-3x mb-3"></i>
                                <p>Upload a document to see preview</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Hidden elements for compatibility -->
        <div style="display: none;">
            <div id="pageNumberSection"></div>
            <select id="outputFormat"><option value="table">Table</option></select>
            <select id="modelSelect"><option value="ultra-de-vlm-v1">Ultra DE VLM v1</option></select>
            <input id="pageNumber" value="">
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Test version of the submit button functionality
        let currentFile = null;
        let currentFileType = null;
        let isProcessing = false;
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            
            const fileInput = document.getElementById('fileInput');
            const textContent = document.getElementById('textContent');
            const submitBtn = document.getElementById('submitBtn');
            const submitStatus = document.getElementById('submitStatus');
            const inputModeRadios = document.querySelectorAll('input[name="inputMode"]');
            const fileUploadSection = document.getElementById('fileUploadSection');
            const textInputSection = document.getElementById('textInputSection');
            const previewContainer = document.getElementById('previewContainer');
            
            function updateSubmitButton() {
                const inputMode = document.querySelector('input[name="inputMode"]:checked')?.value || 'file';
                const textValue = textContent ? textContent.value.trim() : '';
                
                let canProcess = false;
                let statusMessage = '';
                
                if (inputMode === 'text' && textValue) {
                    canProcess = true;
                    statusMessage = `Ready to process ${textValue.length} characters of text`;
                } else if (inputMode === 'file' && currentFile) {
                    canProcess = true;
                    statusMessage = `Ready to process file: ${currentFile.name}`;
                } else if (inputMode === 'text') {
                    statusMessage = 'Please enter text content to process';
                } else {
                    statusMessage = 'Please upload a file to process';
                }
                
                submitBtn.disabled = !canProcess || isProcessing;
                submitStatus.textContent = statusMessage;
                
                submitBtn.classList.remove('btn-secondary', 'btn-success', 'btn-warning');
                submitStatus.classList.remove('text-muted', 'text-success', 'text-warning');
                
                if (canProcess && !isProcessing) {
                    submitBtn.innerHTML = '<i class="fas fa-play me-2"></i>Submit & Process Document';
                    submitBtn.classList.add('btn-success');
                    submitStatus.classList.add('text-success');
                } else if (isProcessing) {
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                    submitBtn.classList.add('btn-warning');
                    submitStatus.classList.add('text-warning');
                } else {
                    submitBtn.innerHTML = '<i class="fas fa-upload me-2"></i>Submit & Process Document';
                    submitBtn.classList.add('btn-secondary');
                    submitStatus.classList.add('text-muted');
                }
                
                console.log('Submit button updated:', { canProcess, isProcessing, disabled: submitBtn.disabled });
            }
            
            function showDocumentPreview(file, fileType) {
                console.log('Showing preview for:', file.name, fileType);
                
                previewContainer.innerHTML = `
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>File Uploaded Successfully</h6>
                        <p class="mb-0"><strong>File:</strong> ${file.name}</p>
                        <p class="mb-0"><strong>Type:</strong> ${fileType}</p>
                        <p class="mb-0"><strong>Size:</strong> ${(file.size / 1024).toFixed(2)} KB</p>
                    </div>
                `;
            }
            
            // Input mode change handlers
            inputModeRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    const mode = this.value;
                    
                    if (mode === 'file') {
                        fileUploadSection.style.display = 'block';
                        textInputSection.style.display = 'none';
                    } else {
                        fileUploadSection.style.display = 'none';
                        textInputSection.style.display = 'block';
                    }
                    
                    updateSubmitButton();
                });
            });
            
            // File input handler
            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                
                if (file) {
                    currentFile = file;
                    currentFileType = file.type.includes('pdf') ? 'pdf' : 
                                   file.type.includes('image') ? 'image' : 'text';
                    
                    showDocumentPreview(file, currentFileType);
                } else {
                    currentFile = null;
                    currentFileType = null;
                    previewContainer.innerHTML = `
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-file-alt fa-3x mb-3"></i>
                            <p>Upload a document to see preview</p>
                        </div>
                    `;
                }
                
                updateSubmitButton();
            });
            
            // Text input handler
            textContent.addEventListener('input', function() {
                updateSubmitButton();
            });
            
            // Submit button handler
            submitBtn.addEventListener('click', function() {
                console.log('Submit button clicked!');
                alert('Submit button is working! In the real app, this would process the document.');
            });
            
            // Initialize
            updateSubmitButton();
            console.log('Test page initialization complete');
        });
    </script>
</body>
</html>
