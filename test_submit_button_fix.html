<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Submit Button Test - FIXED</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>🔧 Submit Button Test - FIXED VERSION</h2>
        
        <!-- Status Display -->
        <div class="alert alert-info">
            <h5>Test Status:</h5>
            <div id="testStatus">Initializing...</div>
        </div>
        
        <!-- Input Mode Selection -->
        <div class="mb-3">
            <label class="form-label fw-bold">Input Mode:</label>
            <div>
                <input type="radio" name="inputMode" value="file" id="fileMode" checked>
                <label for="fileMode">File Upload</label>
                
                <input type="radio" name="inputMode" value="text" id="textMode" class="ms-3">
                <label for="textMode">Text Input</label>
            </div>
        </div>
        
        <!-- File Upload Section -->
        <div id="fileUploadSection" class="mb-3">
            <label for="fileInput" class="form-label fw-bold">Upload Document:</label>
            <input type="file" class="form-control" id="fileInput" accept=".pdf,.png,.jpg,.jpeg,.txt">
        </div>
        
        <!-- Text Input Section -->
        <div id="textInputSection" class="mb-3" style="display: none;">
            <label for="textContent" class="form-label fw-bold">Text Content:</label>
            <textarea class="form-control" id="textContent" rows="5" placeholder="Paste your text content here..."></textarea>
        </div>
        
        <!-- Prompt Input -->
        <div class="mb-3">
            <label for="promptInput" class="form-label fw-bold">Extraction Prompt:</label>
            <input type="text" class="form-control" id="promptInput" value="Extract important information." placeholder="Enter your extraction prompt...">
        </div>
        
        <!-- Model Selection -->
        <div class="mb-3">
            <label for="modelSelect" class="form-label fw-bold">Model:</label>
            <select class="form-select" id="modelSelect">
                <option value="ultra-de-vlm-v1">Ultra DE VLM v1 (Vision + Text)</option>
                <option value="de-q2.5VL-3B">Qwen2.5-VL 3B (Vision + Text)</option>
                <option value="ultra-de-v1">Ultra DE v1 (Text Only)</option>
            </select>
        </div>
        
        <!-- Output Format -->
        <div class="mb-3">
            <label for="outputFormat" class="form-label fw-bold">Output Format:</label>
            <select class="form-select" id="outputFormat">
                <option value="key-value">Key-Value Pairs</option>
                <option value="table">Table Format</option>
                <option value="bullet-points">Bullet Points</option>
            </select>
        </div>
        
        <!-- Submit Button -->
        <div class="mb-4">
            <button type="button" class="btn btn-success btn-lg w-100" id="submitBtn" disabled>
                <i class="fas fa-play me-2"></i>Submit & Process Document
            </button>
            <div class="form-text text-center mt-2">
                <small id="submitStatus" class="text-muted">Please upload a file or enter text to enable processing</small>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="card">
            <div class="card-header">
                <h5>Test Results</h5>
            </div>
            <div class="card-body">
                <div id="testResults">No tests run yet</div>
            </div>
        </div>
        
        <!-- Results Section -->
        <div id="resultsSection" class="mt-4" style="display: none;">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5>Processing Results</h5>
                    <span id="processingTime" class="badge bg-light text-dark"></span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead id="tableHeaders"></thead>
                            <tbody id="tableBody"></tbody>
                        </table>
                    </div>
                    <div class="mt-3">
                        <h6>Raw Output:</h6>
                        <pre id="rawOutput" class="bg-light p-3"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5 id="loadingText">Processing...</h5>
                    <p class="text-muted mb-0" id="loadingSubtext">Please wait while we process your document</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Container -->
    <div id="alertContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Test Script -->
    <script>
        // Test the submit button functionality
        document.addEventListener('DOMContentLoaded', function() {
            const testStatus = document.getElementById('testStatus');
            const testResults = document.getElementById('testResults');
            
            testStatus.innerHTML = '✅ DOM loaded, testing submit button...';
            
            // Test if elements exist
            const submitBtn = document.getElementById('submitBtn');
            const fileInput = document.getElementById('fileInput');
            const textContent = document.getElementById('textContent');
            
            let results = [];
            
            if (submitBtn) {
                results.push('✅ Submit button found');
            } else {
                results.push('❌ Submit button NOT found');
            }
            
            if (fileInput) {
                results.push('✅ File input found');
            } else {
                results.push('❌ File input NOT found');
            }
            
            if (textContent) {
                results.push('✅ Text content found');
            } else {
                results.push('❌ Text content NOT found');
            }
            
            // Test button click
            if (submitBtn) {
                submitBtn.addEventListener('click', function() {
                    results.push('✅ Submit button click event fired!');
                    testResults.innerHTML = results.join('<br>');
                    alert('🎉 Submit button is working!\n\nThe button click event was successfully triggered.');
                });
                results.push('✅ Submit button click handler attached');
            }
            
            testResults.innerHTML = results.join('<br>');
            testStatus.innerHTML = '✅ Test completed - check results below';
        });
    </script>
</body>
</html>
