#!/usr/bin/env python3
"""
Test script to verify the web interface functionality
"""

import requests
import json
import os

def test_upload_and_process():
    """Test file upload and processing"""
    base_url = "http://localhost:5000"
    
    print("🧪 Testing VLM Web Interface...")
    print("=" * 50)
    
    # Test 1: Check if server is running
    try:
        response = requests.get(base_url)
        print(f"✅ Server Status: {response.status_code}")
    except Exception as e:
        print(f"❌ Server not accessible: {e}")
        return
    
    # Test 2: Test text upload
    print("\n📝 Testing text upload...")
    text_data = {
        'text_content': 'Borrower Name: <PERSON>\nLoan Amount: $250,000\nProperty: 123 Main St'
    }
    
    try:
        response = requests.post(f"{base_url}/upload_text", json=text_data)
        if response.status_code == 200:
            print("✅ Text upload successful")
            result = response.json()
            print(f"   Response: {result.get('message', 'Success')}")
        else:
            print(f"❌ Text upload failed: {response.status_code}")
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"❌ Text upload error: {e}")
    
    # Test 3: Test file upload
    print("\n📄 Testing file upload...")
    test_file_path = "test_document.txt"
    
    if os.path.exists(test_file_path):
        try:
            with open(test_file_path, 'rb') as f:
                files = {'file': f}
                response = requests.post(f"{base_url}/upload", files=files)
                
            if response.status_code == 200:
                print("✅ File upload successful")
                result = response.json()
                print(f"   Filename: {result.get('filename', 'Unknown')}")
                print(f"   File Type: {result.get('file_type', 'Unknown')}")
            else:
                print(f"❌ File upload failed: {response.status_code}")
                print(f"   Error: {response.text}")
        except Exception as e:
            print(f"❌ File upload error: {e}")
    else:
        print(f"❌ Test file not found: {test_file_path}")
    
    # Test 4: Test processing
    print("\n🤖 Testing document processing...")
    process_data = {
        'prompt': 'Extract borrower information',
        'input_format': 'key-value',
        'model_type': 'ultra-de-v1'  # Text-only model for testing
    }
    
    try:
        response = requests.post(f"{base_url}/process", json=process_data)
        if response.status_code == 200:
            print("✅ Processing successful")
            result = response.json()
            print(f"   Processing time: {result.get('elapsed_time', 'Unknown')}s")
            print(f"   Structured data: {len(result.get('structured_data', []))} records")
        else:
            print(f"❌ Processing failed: {response.status_code}")
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"❌ Processing error: {e}")
    
    # Test 5: Test models endpoint
    print("\n🧠 Testing models endpoint...")
    try:
        response = requests.get(f"{base_url}/models")
        if response.status_code == 200:
            print("✅ Models endpoint working")
            models = response.json()
            print(f"   Available models: {len(models.get('models', []))}")
            for model in models.get('models', []):
                print(f"     • {model}")
        else:
            print(f"❌ Models endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Models endpoint error: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Test completed!")

if __name__ == "__main__":
    test_upload_and_process()
