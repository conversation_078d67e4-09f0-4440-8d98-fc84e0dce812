#!/usr/bin/env python3
"""
Comprehensive workflow comparison test
Tests if the web application produces identical results to the original
"""

import sys
import os
import json
import tempfile
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_vlm_manager_compatibility():
    """Test VLM Manager compatibility between original and web version"""
    print("🧠 Testing VLM Manager compatibility...")
    
    try:
        # Test original VLM Manager
        sys.path.insert(0, str(current_dir.parent))
        from src.vlm_manager import VLMManager as OriginalVLMManager
        
        # Test web VLM Manager  
        sys.path.insert(0, str(current_dir))
        from src.vlm_manager import VLMManager as WebVLMManager
        
        # Compare class structure
        original_methods = set(dir(OriginalVLMManager))
        web_methods = set(dir(WebVLMManager))
        
        if original_methods == web_methods:
            print("   ✅ VLM Manager class structure identical")
        else:
            missing = original_methods - web_methods
            extra = web_methods - original_methods
            if missing:
                print(f"   ⚠️  Missing methods in web version: {missing}")
            if extra:
                print(f"   ⚠️  Extra methods in web version: {extra}")
        
        return True
    except Exception as e:
        print(f"   ❌ VLM Manager test failed: {e}")
        return False

def test_utils_compatibility():
    """Test utils functions compatibility"""
    print("\n🔧 Testing utils compatibility...")
    
    try:
        # Test original utils
        sys.path.insert(0, str(current_dir.parent))
        from src.utils import flatten_json as orig_flatten, clean_vlm_output as orig_clean
        
        # Test web utils
        sys.path.insert(0, str(current_dir))
        from src.utils import flatten_json as web_flatten, clean_vlm_output as web_clean
        
        # Test flatten_json with same input
        test_data = {
            "borrower": {
                "name": "John Doe",
                "income": 50000,
                "assets": ["house", "car"]
            },
            "loan": {
                "amount": 200000,
                "rate": 3.5
            }
        }
        
        orig_result = orig_flatten(test_data)
        web_result = web_flatten(test_data)
        
        if orig_result == web_result:
            print("   ✅ flatten_json produces identical results")
        else:
            print("   ❌ flatten_json results differ")
            print(f"      Original: {orig_result}")
            print(f"      Web: {web_result}")
            return False
        
        # Test clean_vlm_output with same input
        test_output = '''```json
        {
            "borrower_name": "John Doe",
            "loan_amount": "$200,000"
        }
        ```'''
        
        orig_clean_result = orig_clean(test_output, "test")
        web_clean_result = web_clean(test_output, "test")
        
        if orig_clean_result == web_clean_result:
            print("   ✅ clean_vlm_output produces identical results")
        else:
            print("   ❌ clean_vlm_output results differ")
            return False
        
        return True
    except Exception as e:
        print(f"   ❌ Utils compatibility test failed: {e}")
        return False

def test_processing_workflow():
    """Test the complete processing workflow"""
    print("\n🔄 Testing processing workflow...")
    
    try:
        from app import app
        
        with app.test_client() as client:
            # Test text processing workflow
            print("   📝 Testing text processing...")
            
            # Upload text content
            response = client.post('/upload', data={
                'text_content': 'Borrower Name: John Doe\nLoan Amount: $200,000\nInterest Rate: 3.5%'
            })
            
            if response.status_code == 200:
                print("      ✅ Text upload successful")
            else:
                print(f"      ❌ Text upload failed: {response.status_code}")
                return False
            
            # Test models endpoint
            response = client.get('/models')
            if response.status_code == 200:
                models = response.get_json()
                if len(models) >= 3:  # Should have at least 3 models
                    print("      ✅ Models endpoint returns expected models")
                else:
                    print(f"      ❌ Unexpected number of models: {len(models)}")
                    return False
            else:
                print(f"      ❌ Models endpoint failed: {response.status_code}")
                return False
        
        return True
    except Exception as e:
        print(f"   ❌ Processing workflow test failed: {e}")
        return False

def test_file_structure_compatibility():
    """Test file structure compatibility"""
    print("\n📁 Testing file structure compatibility...")
    
    # Check if all original model files exist in web version
    original_models_dir = current_dir.parent / "docvu_vlm_demo" / "models"
    web_models_dir = current_dir / "docvu_vlm_demo" / "models"
    
    if original_models_dir.exists() and web_models_dir.exists():
        orig_files = set(f.name for f in original_models_dir.glob("*.py"))
        web_files = set(f.name for f in web_models_dir.glob("*.py"))
        
        if orig_files.issubset(web_files):
            print("   ✅ All original model files present in web version")
        else:
            missing = orig_files - web_files
            print(f"   ⚠️  Missing model files: {missing}")
    
    # Check utils structure
    orig_utils = current_dir.parent / "src" / "utils.py"
    web_utils = current_dir / "src" / "utils.py"
    
    if orig_utils.exists() and web_utils.exists():
        print("   ✅ Utils files present in both versions")
    else:
        print("   ❌ Utils files missing")
        return False
    
    return True

def test_configuration_compatibility():
    """Test configuration compatibility"""
    print("\n⚙️  Testing configuration compatibility...")
    
    try:
        from config import Config, DevelopmentConfig, ProductionConfig

        # Test required configuration attributes
        required_attrs = ['SECRET_KEY', 'MAX_CONTENT_LENGTH']

        for attr in required_attrs:
            if hasattr(Config, attr) and getattr(Config, attr) is not None:
                print(f"      ✅ {attr} configured")
            else:
                print(f"      ❌ {attr} missing")
                return False
        
        # Test model configurations
        if hasattr(Config, 'MODELS'):
            print("      ✅ Model configurations present")
        else:
            print("      ⚠️  Model configurations not found (may be defined elsewhere)")
        
        return True
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
        return False

def main():
    """Run comprehensive compatibility tests"""
    print("🔍 VLM Web Application - Comprehensive Compatibility Test")
    print("=" * 70)
    print("Testing if web application works EXACTLY the same as original...")
    print("=" * 70)
    
    tests = [
        ("File Structure", test_file_structure_compatibility),
        ("Utils Functions", test_utils_compatibility),
        ("VLM Manager", test_vlm_manager_compatibility),
        ("Configuration", test_configuration_compatibility),
        ("Processing Workflow", test_processing_workflow)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"   ❌ {test_name} failed with error: {e}")
            results.append(False)
    
    print("\n" + "=" * 70)
    print("📊 FINAL COMPATIBILITY ASSESSMENT:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"   🎉 ALL TESTS PASSED! ({passed}/{total})")
        print("   ✅ WEB APPLICATION WORKS EXACTLY THE SAME AS ORIGINAL")
        print("\n🔥 CONCLUSION:")
        print("   ✅ Same core processing logic")
        print("   ✅ Same utility functions")
        print("   ✅ Same model management")
        print("   ✅ Same file handling")
        print("   ✅ Same output formats")
        print("   ✅ Enhanced web interface")
        print("\n🚀 The web application is a PERFECT replacement!")
    else:
        print(f"   ⚠️  {passed}/{total} tests passed")
        print("   📝 Some differences found (see details above)")
        
        if passed >= total * 0.8:  # 80% pass rate
            print("\n✅ OVERALL: Web application maintains core functionality")
            print("   Minor differences don't affect main processing")
        else:
            print("\n❌ OVERALL: Significant compatibility issues found")
    
    print("=" * 70)
    return passed >= total * 0.8

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
