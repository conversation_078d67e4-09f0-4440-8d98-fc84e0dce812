#!/usr/bin/env python3
"""
Complete Code Verification Script
Checks all components of the VLM Web Application
"""

import os
import sys
import json
from pathlib import Path

def print_header(title):
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def print_check(item, status, details=""):
    icon = "✅" if status else "❌"
    print(f"{icon} {item}")
    if details:
        print(f"   {details}")

def check_file_exists(file_path, description):
    exists = os.path.exists(file_path)
    print_check(f"{description}: {file_path}", exists)
    return exists

def check_html_elements():
    print_header("HTML Template Verification")
    
    html_file = "templates/index.html"
    if not os.path.exists(html_file):
        print_check("HTML template file", False, "File not found")
        return False
    
    with open(html_file, 'r') as f:
        content = f.read()
    
    required_elements = [
        ('submitBtn', 'Submit button'),
        ('fileInput', 'File input'),
        ('textContent', 'Text content textarea'),
        ('promptInput', 'Prompt input'),
        ('previewContainer', 'Preview container'),
        ('modelSelect', 'Model selection'),
        ('outputFormat', 'Output format selection'),
        ('pageNumber', 'Page number input'),
        ('submitStatus', 'Submit status text')
    ]
    
    all_present = True
    for element_id, description in required_elements:
        present = f'id="{element_id}"' in content
        print_check(f"{description} (#{element_id})", present)
        if not present:
            all_present = False
    
    return all_present

def check_javascript_functions():
    print_header("JavaScript Functions Verification")
    
    js_file = "static/js/document-processor.js"
    if not os.path.exists(js_file):
        print_check("JavaScript file", False, "File not found")
        return False
    
    with open(js_file, 'r') as f:
        content = f.read()
    
    required_functions = [
        ('updateSubmitButton', 'Submit button update function'),
        ('showDocumentPreview', 'Document preview function'),
        ('updateModelOptionsOld', 'Model options update function'),
        ('showAlert', 'Alert display function'),
        ('submitBtn.*addEventListener.*click', 'Submit button click handler'),
        ('fileInput.*addEventListener.*change', 'File input change handler'),
        ('textContent.*addEventListener.*input', 'Text input change handler')
    ]
    
    all_present = True
    for pattern, description in required_functions:
        import re
        present = bool(re.search(pattern, content))
        print_check(description, present)
        if not present:
            all_present = False
    
    # Check for global variables
    global_vars = ['currentFile', 'currentFileType', 'isProcessing']
    for var in global_vars:
        present = f'let {var}' in content or f'var {var}' in content
        print_check(f"Global variable: {var}", present)
        if not present:
            all_present = False
    
    return all_present

def check_flask_routes():
    print_header("Flask Routes Verification")
    
    app_file = "app.py"
    if not os.path.exists(app_file):
        print_check("Flask app file", False, "File not found")
        return False
    
    with open(app_file, 'r') as f:
        content = f.read()
    
    required_routes = [
        ('@app.route.*upload.*POST', 'File upload route'),
        ('@app.route.*upload_text.*POST', 'Text upload route'),
        ('@app.route.*process.*POST', 'Document processing route'),
        ('@app.route.*download/excel', 'Excel download route'),
        ('@app.route.*download/json', 'JSON download route')
    ]
    
    all_present = True
    for pattern, description in required_routes:
        import re
        present = bool(re.search(pattern, content))
        print_check(description, present)
        if not present:
            all_present = False
    
    return all_present

def check_dependencies():
    print_header("Dependencies Verification")
    
    # Check requirements files
    req_files = [
        "requirements.txt",
        "requirements_fixed.txt", 
        "requirements_complete.txt"
    ]
    
    for req_file in req_files:
        exists = check_file_exists(req_file, f"Requirements file: {req_file}")
    
    # Check if Flask/Jinja2 compatibility is handled
    app_file = "app.py"
    if os.path.exists(app_file):
        with open(app_file, 'r') as f:
            content = f.read()
        
        jinja2_fix = 'from markupsafe import escape' in content or 'from jinja2 import escape' in content
        print_check("Jinja2 compatibility fix", jinja2_fix)
    
    return True

def check_directory_structure():
    print_header("Directory Structure Verification")
    
    required_dirs = [
        "templates",
        "static",
        "static/css",
        "static/js",
        "uploads",
        "temp",
        "temp/images",
        "temp/input_files"
    ]
    
    all_present = True
    for directory in required_dirs:
        exists = os.path.exists(directory)
        print_check(f"Directory: {directory}", exists)
        if not exists:
            all_present = False
            try:
                os.makedirs(directory, exist_ok=True)
                print(f"   📁 Created directory: {directory}")
            except Exception as e:
                print(f"   ❌ Failed to create: {e}")
    
    return all_present

def check_installation_files():
    print_header("Installation Files Verification")
    
    install_files = [
        ("setup_complete.py", "Complete setup script"),
        ("run_simple.py", "Simple runner script"),
        ("run_minimal.py", "Minimal runner script"),
        ("install_compatible.py", "Compatibility installer"),
        ("INSTALL_COMMANDS.md", "Installation commands"),
        ("SUBMIT_BUTTON_FIXES.md", "Submit button fixes documentation")
    ]
    
    all_present = True
    for file_path, description in install_files:
        exists = check_file_exists(file_path, description)
        if not exists:
            all_present = False
    
    return all_present

def generate_test_report():
    print_header("COMPLETE VERIFICATION REPORT")
    
    checks = [
        ("Directory Structure", check_directory_structure()),
        ("HTML Template Elements", check_html_elements()),
        ("JavaScript Functions", check_javascript_functions()),
        ("Flask Routes", check_flask_routes()),
        ("Dependencies", check_dependencies()),
        ("Installation Files", check_installation_files())
    ]
    
    all_passed = True
    for check_name, result in checks:
        print_check(check_name, result)
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL CHECKS PASSED! The application is ready to run.")
        print("\n🚀 To start the application:")
        print("   python setup_complete.py")
        print("\n🌐 Then open: http://localhost:5000")
    else:
        print("⚠️  Some checks failed. Please review the issues above.")
        print("\n🔧 Common fixes:")
        print("   1. Run: python setup_complete.py")
        print("   2. Check file permissions")
        print("   3. Verify all files are present")
    
    print("=" * 60)
    
    return all_passed

def main():
    print("🔍 VLM Web Application - Complete Code Verification")
    print("This script checks all components for proper functionality")
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Run all checks
    result = generate_test_report()
    
    # Exit with appropriate code
    sys.exit(0 if result else 1)

if __name__ == "__main__":
    main()
